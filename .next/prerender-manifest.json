{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "e10870c837ed7a2621878246c79c1d74", "previewModeSigningKey": "be935ede3feea5a5a351da6277b6516c2b40426a6c6e72d08348176357f9390f", "previewModeEncryptionKey": "3210b69ccb16e46ac913b49e741270b07f98451a31d16b028d458e5e036ed6bb"}}