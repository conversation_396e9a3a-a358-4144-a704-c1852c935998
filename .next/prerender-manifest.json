{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "18c21221e35effebbccb6de11baae60b", "previewModeSigningKey": "5cc6fac61986d9510655e2c20641609490b88afc185cc639fd041a274e1e3f37", "previewModeEncryptionKey": "442fbbda36a22f56edf37f0ce83a7db6decc9ae928a600ba590be18831095386"}}