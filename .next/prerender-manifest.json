{"version": 4, "routes": {"/": {"experimentalBypassFor": [{"type": "header", "key": "Next-Action"}, {"type": "header", "key": "content-type", "value": "multipart/form-data;.*"}], "initialRevalidateSeconds": false, "srcRoute": "/", "dataRoute": "/index.rsc", "allowHeader": ["host", "x-matched-path", "x-prerender-revalidate", "x-prerender-revalidate-if-generated", "x-next-revalidated-tags", "x-next-revalidate-tag-token"]}}, "dynamicRoutes": {}, "notFoundRoutes": [], "preview": {"previewModeId": "d81bfd0312544697e72d9e2a37ce3482", "previewModeSigningKey": "5d3271fc296fb9f431513f244cdb8ab46e610634f9d684b44a89d8e621d23421", "previewModeEncryptionKey": "8267aed17fce9df8ddf6f15e96f79e7f12b626fc67aafa722f50f1acb20da8cf"}}