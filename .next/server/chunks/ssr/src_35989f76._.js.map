{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/layout/Header.tsx"], "sourcesContent": ["\n'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\n\nconst Header = () => {\n  const [scrolled, setScrolled] = useState(false);\n  const [menuOpen, setMenuOpen] = useState(false);\n\n  useEffect(() => {\n    if (typeof window === 'undefined') return;\n\n    const handleScroll = () => {\n      const isScrolled = window.scrollY > 20;\n      if (isScrolled !== scrolled) {\n        setScrolled(isScrolled);\n      }\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, [scrolled]);\n\n  const navItems = [\n    { name: 'About', href: '#about' },\n    { name: 'Skills', href: '#skills' },\n    { name: 'Projects', href: '#projects' },\n    { name: 'Experience', href: '#experience' },\n    { name: 'Contact', href: '#contact' },\n  ];\n\n  return (\n    <motion.header\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.5 }}\n      className={`fixed top-0 w-full z-50 transition-all duration-300 ${\n        scrolled\n          ? 'bg-github-dark/80 backdrop-blur-md shadow-lg border-b border-github-border'\n          : 'bg-transparent'\n      }`}\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center py-4\">\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"w-10 h-10 rounded-full bg-gradient-to-br from-neon-green to-neon-blue flex items-center justify-center\">\n              <span className=\"font-mono font-bold text-white\">GH</span>\n            </div>\n            <span className=\"font-bold text-xl\">GreenHacker</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            {navItems.map((item) => (\n              <a\n                key={item.name}\n                href={item.href}\n                className=\"text-github-text hover:text-white transition-colors group relative\"\n              >\n                <span>{item.name}</span>\n                <span className=\"absolute -bottom-1 left-0 w-0 h-0.5 bg-neon-green transition-all group-hover:w-full\" />\n              </a>\n            ))}\n          </nav>\n\n          {/* Mobile menu button */}\n          <button\n            onClick={() => setMenuOpen(!menuOpen)}\n            className=\"md:hidden text-gray-300 hover:text-white\"\n            aria-label=\"Toggle menu\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n              className=\"w-6 h-6\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d={menuOpen ? \"M6 18L18 6M6 6l12 12\" : \"M4 6h16M4 12h16M4 18h16\"}\n              />\n            </svg>\n          </button>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      <motion.div\n        initial={false}\n        animate={{ height: menuOpen ? \"auto\" : 0, opacity: menuOpen ? 1 : 0 }}\n        transition={{ duration: 0.3 }}\n        className=\"md:hidden overflow-hidden\"\n      >\n        <div className=\"px-4 py-2 space-y-1 bg-github-light border-t border-github-border\">\n          {navItems.map((item) => (\n            <a\n              key={item.name}\n              href={item.href}\n              className=\"block py-2 px-4 text-github-text hover:bg-github-dark hover:text-white rounded-md\"\n              onClick={() => setMenuOpen(false)}\n            >\n              {item.name}\n            </a>\n          ))}\n        </div>\n      </motion.div>\n    </motion.header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAJA;;;;;AAMA,MAAM,SAAS;IACb,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAmC;;QAEnC,MAAM;IASR,GAAG;QAAC;KAAS;IAEb,MAAM,WAAW;QACf;YAAE,MAAM;YAAS,MAAM;QAAS;QAChC;YAAE,MAAM;YAAU,MAAM;QAAU;QAClC;YAAE,MAAM;YAAY,MAAM;QAAY;QACtC;YAAE,MAAM;YAAc,MAAM;QAAc;QAC1C;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS;YAAE,GAAG,CAAC;QAAI;QACnB,SAAS;YAAE,GAAG;QAAE;QAChB,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAC,oDAAoD,EAC9D,WACI,+EACA,kBACJ;;0BAEF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAiC;;;;;;;;;;;8CAEnD,8OAAC;oCAAK,WAAU;8CAAoB;;;;;;;;;;;;sCAItC,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC;oCAEC,MAAM,KAAK,IAAI;oCACf,WAAU;;sDAEV,8OAAC;sDAAM,KAAK,IAAI;;;;;;sDAChB,8OAAC;4CAAK,WAAU;;;;;;;mCALX,KAAK,IAAI;;;;;;;;;;sCAWpB,8OAAC;4BACC,SAAS,IAAM,YAAY,CAAC;4BAC5B,WAAU;4BACV,cAAW;sCAEX,cAAA,8OAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,WAAU;0CAEV,cAAA,8OAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAG,WAAW,yBAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQjD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;gBACT,SAAS;oBAAE,QAAQ,WAAW,SAAS;oBAAG,SAAS,WAAW,IAAI;gBAAE;gBACpE,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC;4BAEC,MAAM,KAAK,IAAI;4BACf,WAAU;4BACV,SAAS,IAAM,YAAY;sCAE1B,KAAK,IAAI;2BALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;AAY5B;uCAEe", "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/layout/Footer.tsx"], "sourcesContent": ["\n'use client';\n\nimport { motion } from 'framer-motion';\nimport { Instagram, Linkedin } from 'lucide-react';\n\nconst Footer = () => {\n  return (\n    <motion.footer\n      initial={{ opacity: 0 }}\n      whileInView={{ opacity: 1 }}\n      transition={{ duration: 0.5 }}\n      className=\"bg-github-darker border-t border-github-border\"\n    >\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          {/* Column 1: Logo and info */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-10 h-10 rounded-full bg-gradient-to-br from-neon-green to-neon-blue flex items-center justify-center\">\n                <span className=\"font-mono font-bold text-white\">GH</span>\n              </div>\n              <span className=\"font-bold text-xl text-white\">GreenHacker</span>\n            </div>\n            <p className=\"text-sm text-github-text mt-4 max-w-sm\">\n              Passionate developer and open-source contributor currently working on a photo-sharing platform with face recognition.\n            </p>\n          </div>\n\n          {/* Column 2: Quick Links */}\n          <div>\n            <h3 className=\"text-white font-medium mb-4\">Quick Links</h3>\n            <ul className=\"space-y-2\">\n              {['About', 'Projects', 'Skills', 'Experience', 'Contact'].map((item) => (\n                <li key={item}>\n                  <a\n                    href={`#${item.toLowerCase()}`}\n                    className=\"text-github-text hover:text-white transition-colors hover:underline\"\n                  >\n                    {item}\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Column 3: Social and Contact */}\n          <div>\n            <h3 className=\"text-white font-medium mb-4\">Connect With Me</h3>\n            <div className=\"flex space-x-4\">\n              <a\n                href=\"https://instagram.com\"\n                target=\"_blank\"\n                rel=\"noreferrer\"\n                className=\"bg-github-light p-2 rounded-full hover:bg-neon-green/20 transition-colors\"\n              >\n                <Instagram className=\"w-5 h-5 text-github-text hover:text-white\" />\n              </a>\n              <a\n                href=\"https://linkedin.com\"\n                target=\"_blank\"\n                rel=\"noreferrer\"\n                className=\"bg-github-light p-2 rounded-full hover:bg-neon-blue/20 transition-colors\"\n              >\n                <Linkedin className=\"w-5 h-5 text-github-text hover:text-white\" />\n              </a>\n            </div>\n\n            <div className=\"mt-4\">\n              <p className=\"text-sm text-github-text\">harsh_hirawat</p>\n              <p className=\"text-sm text-github-text mt-1\">Pune, Maharashtra</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"mt-8 pt-8 border-t border-github-border text-center\">\n          <p className=\"text-sm text-github-text\">\n            © {new Date().getFullYear()} GreenHacker. All rights reserved.\n          </p>\n        </div>\n      </div>\n    </motion.footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAHA;;;;AAKA,MAAM,SAAS;IACb,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS;YAAE,SAAS;QAAE;QACtB,aAAa;YAAE,SAAS;QAAE;QAC1B,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAU;kBAEV,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAiC;;;;;;;;;;;sDAEnD,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;;8CAEjD,8OAAC;oCAAE,WAAU;8CAAyC;;;;;;;;;;;;sCAMxD,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,8OAAC;oCAAG,WAAU;8CACX;wCAAC;wCAAS;wCAAY;wCAAU;wCAAc;qCAAU,CAAC,GAAG,CAAC,CAAC,qBAC7D,8OAAC;sDACC,cAAA,8OAAC;gDACC,MAAM,CAAC,CAAC,EAAE,KAAK,WAAW,IAAI;gDAC9B,WAAU;0DAET;;;;;;2CALI;;;;;;;;;;;;;;;;sCAaf,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA8B;;;;;;8CAC5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,8OAAC,4MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAIxB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDAA2B;;;;;;sDACxC,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;8BAKnD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;;4BAA2B;4BACnC,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAMxC;uCAEe", "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/hero/ThreeFallback.tsx"], "sourcesContent": ["\nimport React from 'react';\n\nconst ThreeFallback = () => (\n  <div className=\"absolute inset-0 bg-github-darker z-0 opacity-80\">\n    <div className=\"absolute inset-0 opacity-30\">\n      <div className=\"absolute top-0 -left-4 w-72 h-72 bg-neon-purple rounded-full mix-blend-screen filter blur-xl opacity-70 animate-float\"></div>\n      <div className=\"absolute top-8 -right-4 w-72 h-72 bg-neon-green rounded-full mix-blend-screen filter blur-xl opacity-70 animate-float\" style={{ animationDelay: '2s' }}></div>\n      <div className=\"absolute -bottom-8 left-20 w-72 h-72 bg-neon-blue rounded-full mix-blend-screen filter blur-xl opacity-70 animate-float\" style={{ animationDelay: '4s' }}></div>\n    </div>\n  </div>\n);\n\nexport default ThreeFallback;\n"], "names": [], "mappings": ";;;;;AAGA,MAAM,gBAAgB,kBACpB,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BACf,8OAAC;oBAAI,WAAU;oBAAwH,OAAO;wBAAE,gBAAgB;oBAAK;;;;;;8BACrK,8OAAC;oBAAI,WAAU;oBAA0H,OAAO;wBAAE,gBAAgB;oBAAK;;;;;;;;;;;;;;;;;uCAK9J", "debugId": null}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/hero/ThreeDBackground.tsx"], "sourcesContent": ["\n'use client';\n\nimport React, { Suspense } from 'react';\nimport { ErrorBoundary } from 'react-error-boundary';\nimport ThreeFallback from './ThreeFallback';\nimport dynamic from 'next/dynamic';\n\n// Dynamically import Canvas to prevent SSR issues\nconst Canvas = dynamic(\n  () => import('@react-three/fiber').then((mod) => ({ default: mod.Canvas })),\n  { ssr: false }\n);\n\n// Dynamically import InteractiveThreeScene\nconst InteractiveThreeScene = dynamic(() => import('../../3d/InteractiveThreeScene'), {\n  ssr: false,\n  loading: () => <ThreeFallback />\n});\n\ninterface ThreeDBackgroundProps {\n  mounted: boolean;\n}\n\nconst ThreeDBackground = ({ mounted }: ThreeDBackgroundProps) => {\n  return (\n    <div className=\"absolute inset-0 z-0\">\n      <ErrorBoundary\n        fallback={<ThreeFallback />}\n        onError={(error, errorInfo) => {\n          console.error('3D Background Error:', error, errorInfo);\n        }}\n      >\n        <Suspense fallback={<ThreeFallback />}>\n          {mounted && Canvas && (\n            <Canvas\n              camera={{ position: [0, 0, 6], fov: 50 }}\n              dpr={[1, 2]} // Optimize performance by limiting pixel ratio\n              style={{ background: 'transparent' }}\n              gl={{\n                antialias: true,\n                alpha: true,\n                powerPreference: 'high-performance',\n                preserveDrawingBuffer: false,\n                failIfMajorPerformanceCaveat: false\n              }}\n              onCreated={({ gl }) => {\n                // Ensure WebGL context is properly initialized\n                gl.setClearColor(0x000000, 0);\n                console.log('3D Canvas initialized successfully');\n              }}\n              onError={(error) => {\n                console.error('Canvas error:', error);\n              }}\n            >\n              <InteractiveThreeScene />\n            </Canvas>\n          )}\n        </Suspense>\n      </ErrorBoundary>\n    </div>\n  );\n};\n\nexport default ThreeDBackground;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;;;AALA;;;;;;AAOA,kDAAkD;AAClD,MAAM,SAAS,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAEjB,KAAK;;AAGT,2CAA2C;AAC3C,MAAM,wBAAwB,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAClC,KAAK;IACL,SAAS,kBAAM,8OAAC,uJAAA,CAAA,UAAa;;;;;;AAO/B,MAAM,mBAAmB,CAAC,EAAE,OAAO,EAAyB;IAC1D,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,+LAAA,CAAA,gBAAa;YACZ,wBAAU,8OAAC,uJAAA,CAAA,UAAa;;;;;YACxB,SAAS,CAAC,OAAO;gBACf,QAAQ,KAAK,CAAC,wBAAwB,OAAO;YAC/C;sBAEA,cAAA,8OAAC,qMAAA,CAAA,WAAQ;gBAAC,wBAAU,8OAAC,uJAAA,CAAA,UAAa;;;;;0BAC/B,WAAW,wBACV,8OAAC;oBACC,QAAQ;wBAAE,UAAU;4BAAC;4BAAG;4BAAG;yBAAE;wBAAE,KAAK;oBAAG;oBACvC,KAAK;wBAAC;wBAAG;qBAAE;oBACX,OAAO;wBAAE,YAAY;oBAAc;oBACnC,IAAI;wBACF,WAAW;wBACX,OAAO;wBACP,iBAAiB;wBACjB,uBAAuB;wBACvB,8BAA8B;oBAChC;oBACA,WAAW,CAAC,EAAE,EAAE,EAAE;wBAChB,+CAA+C;wBAC/C,GAAG,aAAa,CAAC,UAAU;wBAC3B,QAAQ,GAAG,CAAC;oBACd;oBACA,SAAS,CAAC;wBACR,QAAQ,KAAK,CAAC,iBAAiB;oBACjC;8BAEA,cAAA,8OAAC;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;uCAEe", "debugId": null}}, {"offset": {"line": 645, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/hero/IntroMessage.tsx"], "sourcesContent": ["\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\nconst IntroMessage = () => {\n  return (\n    <div className=\"max-w-3xl\">\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.5 }}\n        className=\"overflow-hidden\"\n      >\n        <h2 className=\"text-neon-green text-lg md:text-xl font-mono mb-2 flex items-center\">\n          <span className=\"wave-emoji mr-2 inline-block\">👋</span> Hello, I'm\n        </h2>\n      </motion.div>\n      \n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.5, delay: 0.2 }}\n      >\n        <h1 className=\"text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-3 relative\">\n          Green Hacker\n          <span className=\"absolute -bottom-2 left-0 h-1 bg-neon-green w-0 animate-expand\"></span>\n        </h1>\n      </motion.div>\n      \n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.5, delay: 0.4 }}\n      >\n        <h2 className=\"text-xl md:text-2xl text-github-text font-medium mb-6 flex flex-wrap gap-2 md:gap-4\">\n          <span className=\"flex items-center\"><span className=\"bg-neon-green w-2 h-2 rounded-full mr-2\"></span>Full Stack Developer</span>\n          <span className=\"flex items-center\"><span className=\"bg-neon-purple w-2 h-2 rounded-full mr-2\"></span>ML Expert</span>\n          <span className=\"flex items-center\"><span className=\"bg-neon-blue w-2 h-2 rounded-full mr-2\"></span>OSS Contributor</span>\n        </h2>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default IntroMessage;\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,MAAM,eAAe;IACnB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;0BAEV,cAAA,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC;4BAAK,WAAU;sCAA+B;;;;;;wBAAS;;;;;;;;;;;;0BAI5D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;0BAExC,cAAA,8OAAC;oBAAG,WAAU;;wBAAsE;sCAElF,8OAAC;4BAAK,WAAU;;;;;;;;;;;;;;;;;0BAIpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;0BAExC,cAAA,8OAAC;oBAAG,WAAU;;sCACZ,8OAAC;4BAAK,WAAU;;8CAAoB,8OAAC;oCAAK,WAAU;;;;;;gCAAiD;;;;;;;sCACrG,8OAAC;4BAAK,WAAU;;8CAAoB,8OAAC;oCAAK,WAAU;;;;;;gCAAkD;;;;;;;sCACtG,8OAAC;4BAAK,WAAU;;8CAAoB,8OAAC;oCAAK,WAAU;;;;;;gCAAgD;;;;;;;;;;;;;;;;;;;;;;;;AAK9G;uCAEe", "debugId": null}}, {"offset": {"line": 819, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/hero/CTAButtons.tsx"], "sourcesContent": ["\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\nconst CTAButtons = () => {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5, delay: 0.6 }}\n      className=\"mt-8\"\n    >\n      <p className=\"text-lg text-github-text max-w-2xl mb-8 leading-relaxed\">\n        I'm currently working on a photo-sharing platform with face recognition.\n        Passionate about open-source and applying Machine Learning to solve real-world problems.\n      </p>\n      \n      <div className=\"flex flex-wrap gap-4\">\n        <motion.a\n          href=\"#projects\"\n          className=\"px-6 py-3 bg-neon-green text-black font-medium rounded-md hover:bg-neon-green/90 transition-colors\"\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          View Projects\n        </motion.a>\n        <motion.a\n          href=\"#contact\"\n          className=\"px-6 py-3 bg-transparent border border-neon-green text-neon-green font-medium rounded-md hover:bg-neon-green/10 transition-colors\"\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          Contact Me\n        </motion.a>\n        <motion.a\n          href=\"#resume\"\n          className=\"px-6 py-3 bg-transparent border border-neon-purple text-neon-purple font-medium rounded-md hover:bg-neon-purple/10 transition-colors\"\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          View Resume\n        </motion.a>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default CTAButtons;\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,MAAM,aAAa;IACjB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,OAAO;QAAI;QACxC,WAAU;;0BAEV,8OAAC;gBAAE,WAAU;0BAA0D;;;;;;0BAKvE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,MAAK;wBACL,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;kCACzB;;;;;;kCAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,MAAK;wBACL,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;kCACzB;;;;;;kCAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,MAAK;wBACL,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;kCACzB;;;;;;;;;;;;;;;;;;AAMT;uCAEe", "debugId": null}}, {"offset": {"line": 918, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/hero/TypewriterEffect.tsx"], "sourcesContent": ["\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\n\n// Array of text options to type\nconst typingTexts = [\n  \"Building cool stuff with <PERSON>act & ML\",\n  \"Creating interactive 3D web experiences\",\n  \"Developing with React Three Fiber\",\n  \"Exploring AI and machine learning\",\n  \"Crafting beautiful UI animations\"\n];\n\nconst TypewriterEffect = () => {\n  const [displayText, setDisplayText] = useState(\"\");\n  const [isDeleting, setIsDeleting] = useState(false);\n  const [textIndex, setTextIndex] = useState(0);\n  const [typingSpeed, setTypingSpeed] = useState(100);\n  const [isBlinking, setIsBlinking] = useState(true);\n\n  useEffect(() => {\n    let timer: ReturnType<typeof setTimeout>;\n    const currentText = typingTexts[textIndex];\n    \n    // If we've completed typing the current text\n    if (!isDeleting && displayText === currentText) {\n      // Pause at the end of typing\n      timer = setTimeout(() => {\n        setIsBlinking(true);\n        setIsDeleting(true);\n        setTypingSpeed(50); // Delete faster\n      }, 2000);\n    } \n    // If we've completed deleting the current text\n    else if (isDeleting && displayText === \"\") {\n      setIsDeleting(false);\n      setTextIndex((textIndex + 1) % typingTexts.length);\n      setTypingSpeed(100); // Type at normal speed\n      setIsBlinking(false);\n    }\n    // If we're in the middle of typing or deleting\n    else {\n      setIsBlinking(false);\n      timer = setTimeout(() => {\n        const nextText = isDeleting\n          ? currentText.substring(0, displayText.length - 1)\n          : currentText.substring(0, displayText.length + 1);\n          \n        setDisplayText(nextText);\n      }, typingSpeed);\n    }\n    \n    return () => clearTimeout(timer);\n  }, [displayText, isDeleting, textIndex, typingSpeed]);\n\n  return (\n    <motion.div\n      initial={{ opacity: 0 }}\n      animate={{ opacity: 1 }}\n      transition={{ delay: 0.8, duration: 0.5 }}\n      className=\"mt-12\"\n    >\n      <p className=\"text-github-text text-lg flex items-center\">\n        <span className=\"mr-2\">Currently:</span>\n        <span className=\"text-neon-green font-mono relative\">\n          {displayText}\n          <span \n            className={`absolute inset-y-0 right-[-0.7ch] w-[0.5ch] bg-neon-green ${\n              isBlinking ? 'animate-cursor-blink' : ''\n            }`}\n          />\n        </span>\n      </p>\n    </motion.div>\n  );\n};\n\nexport default TypewriterEffect;\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAEA,gCAAgC;AAChC,MAAM,cAAc;IAClB;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,mBAAmB;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI;QACJ,MAAM,cAAc,WAAW,CAAC,UAAU;QAE1C,6CAA6C;QAC7C,IAAI,CAAC,cAAc,gBAAgB,aAAa;YAC9C,6BAA6B;YAC7B,QAAQ,WAAW;gBACjB,cAAc;gBACd,cAAc;gBACd,eAAe,KAAK,gBAAgB;YACtC,GAAG;QACL,OAEK,IAAI,cAAc,gBAAgB,IAAI;YACzC,cAAc;YACd,aAAa,CAAC,YAAY,CAAC,IAAI,YAAY,MAAM;YACjD,eAAe,MAAM,uBAAuB;YAC5C,cAAc;QAChB,OAEK;YACH,cAAc;YACd,QAAQ,WAAW;gBACjB,MAAM,WAAW,aACb,YAAY,SAAS,CAAC,GAAG,YAAY,MAAM,GAAG,KAC9C,YAAY,SAAS,CAAC,GAAG,YAAY,MAAM,GAAG;gBAElD,eAAe;YACjB,GAAG;QACL;QAEA,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAa;QAAY;QAAW;KAAY;IAEpD,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,YAAY;YAAE,OAAO;YAAK,UAAU;QAAI;QACxC,WAAU;kBAEV,cAAA,8OAAC;YAAE,WAAU;;8BACX,8OAAC;oBAAK,WAAU;8BAAO;;;;;;8BACvB,8OAAC;oBAAK,WAAU;;wBACb;sCACD,8OAAC;4BACC,WAAW,CAAC,0DAA0D,EACpE,aAAa,yBAAyB,IACtC;;;;;;;;;;;;;;;;;;;;;;;AAMd;uCAEe", "debugId": null}}, {"offset": {"line": 1030, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/hero/ScrollPrompt.tsx"], "sourcesContent": ["\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\nconst ScrollPrompt = () => {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: -20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5, delay: 0.8, repeat: Infinity, repeatType: 'reverse', repeatDelay: 1 }}\n      className=\"absolute bottom-10 left-1/2 transform -translate-x-1/2 z-10\"\n    >\n      <a href=\"#about\" className=\"flex flex-col items-center text-github-text hover:text-white transition-colors\">\n        <span className=\"mb-2 text-sm\">Scroll Down</span>\n        <svg\n          xmlns=\"http://www.w3.org/2000/svg\"\n          width=\"24\"\n          height=\"24\"\n          viewBox=\"0 0 24 24\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          strokeWidth=\"2\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n          className=\"animate-bounce\"\n        >\n          <path d=\"M12 5v14\" />\n          <path d=\"m19 12-7 7-7-7\" />\n        </svg>\n      </a>\n    </motion.div>\n  );\n};\n\nexport default ScrollPrompt;\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,MAAM,eAAe;IACnB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,OAAO;YAAK,QAAQ;YAAU,YAAY;YAAW,aAAa;QAAE;QACjG,WAAU;kBAEV,cAAA,8OAAC;YAAE,MAAK;YAAS,WAAU;;8BACzB,8OAAC;oBAAK,WAAU;8BAAe;;;;;;8BAC/B,8OAAC;oBACC,OAAM;oBACN,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,MAAK;oBACL,QAAO;oBACP,aAAY;oBACZ,eAAc;oBACd,gBAAe;oBACf,WAAU;;sCAEV,8OAAC;4BAAK,GAAE;;;;;;sCACR,8OAAC;4BAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;AAKlB;uCAEe", "debugId": null}}, {"offset": {"line": 1118, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/Hero.tsx"], "sourcesContent": ["\n'use client';\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport ThreeDBackground from './hero/ThreeDBackground';\nimport ThreeFallback from './hero/ThreeFallback';\nimport IntroMessage from './hero/IntroMessage';\nimport CTAButtons from './hero/CTAButtons';\nimport TypewriterEffect from './hero/TypewriterEffect';\nimport ScrollPrompt from './hero/ScrollPrompt';\n\nconst Hero = () => {\n  const [mounted, setMounted] = useState(false);\n  const heroRef = useRef<HTMLElement>(null);\n\n  useEffect(() => {\n    // Delay mounting to ensure DOM is ready\n    const timer = setTimeout(() => {\n      setMounted(true);\n    }, 100);\n\n    // Fix hero height to properly fill viewport\n    const updateHeight = () => {\n      if (typeof window !== 'undefined' && heroRef.current) {\n        const windowHeight = window.innerHeight;\n        heroRef.current.style.height = `${windowHeight}px`;\n      }\n    };\n\n    // Initial height set\n    updateHeight();\n\n    // Update on resize\n    if (typeof window !== 'undefined') {\n      window.addEventListener('resize', updateHeight);\n    }\n\n    return () => {\n      clearTimeout(timer);\n      if (typeof window !== 'undefined') {\n        window.removeEventListener('resize', updateHeight);\n      }\n    };\n  }, []);\n\n  return (\n    <section id=\"home\" ref={heroRef} className=\"relative flex items-center overflow-hidden\">\n      {/* Always show fallback background */}\n      <ThreeFallback />\n\n      {/* Three.js Background with Error Boundary - overlaid on top */}\n      {mounted && <ThreeDBackground mounted={mounted} />}\n\n      <div className=\"section-container relative z-10\">\n        <IntroMessage />\n        <CTAButtons />\n        <TypewriterEffect />\n      </div>\n\n      <ScrollPrompt />\n\n      {/* Add CSS for animations */}\n      <style dangerouslySetInnerHTML={{__html: `\n        @keyframes expand {\n          to { width: 100%; }\n        }\n\n        .animate-expand {\n          animation: expand 1.5s ease-out forwards;\n          animation-delay: 0.8s;\n        }\n\n        .wave-emoji {\n          animation: wave 2.5s infinite;\n          transform-origin: 70% 70%;\n          display: inline-block;\n        }\n\n        @keyframes wave {\n          0% { transform: rotate(0deg); }\n          10% { transform: rotate(14deg); }\n          20% { transform: rotate(-8deg); }\n          30% { transform: rotate(14deg); }\n          40% { transform: rotate(-4deg); }\n          50% { transform: rotate(10deg); }\n          60% { transform: rotate(0deg); }\n          100% { transform: rotate(0deg); }\n        }\n\n        .typewriter {\n          overflow: hidden;\n          border-right: .15em solid #3fb950;\n          white-space: nowrap;\n          margin: 0 auto;\n          letter-spacing: .15em;\n          animation: typing 3.5s steps(40, end), blink-caret .75s step-end infinite;\n        }\n\n        @keyframes typing {\n          from { width: 0 }\n          to { width: 100% }\n        }\n\n        @keyframes blink-caret {\n          from, to { border-color: transparent }\n          50% { border-color: #3fb950 }\n        }\n\n        @keyframes float {\n          0% { transform: translateY(0px); }\n          50% { transform: translateY(-20px); }\n          100% { transform: translateY(0px); }\n        }\n\n        .animate-float {\n          animation: float 6s ease-in-out infinite;\n        }\n      `}} />\n    </section>\n  );\n};\n\nexport default Hero;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAUA,MAAM,OAAO;IACX,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAe;IAEpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAwC;QACxC,MAAM,QAAQ,WAAW;YACvB,WAAW;QACb,GAAG;QAEH,4CAA4C;QAC5C,MAAM,eAAe;YACnB,uCAAsD;;YAGtD;QACF;QAEA,qBAAqB;QACrB;QAEA,mBAAmB;QACnB,uCAAmC;;QAEnC;QAEA,OAAO;YACL,aAAa;YACb,uCAAmC;;YAEnC;QACF;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAQ,IAAG;QAAO,KAAK;QAAS,WAAU;;0BAEzC,8OAAC,uJAAA,CAAA,UAAa;;;;;YAGb,yBAAW,8OAAC,0JAAA,CAAA,UAAgB;gBAAC,SAAS;;;;;;0BAEvC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sJAAA,CAAA,UAAY;;;;;kCACb,8OAAC,oJAAA,CAAA,UAAU;;;;;kCACX,8OAAC,0JAAA,CAAA,UAAgB;;;;;;;;;;;0BAGnB,8OAAC,sJAAA,CAAA,UAAY;;;;;0BAGb,8OAAC;gBAAM,yBAAyB;oBAAC,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAuD1C,CAAC;gBAAA;;;;;;;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 1289, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/About.tsx"], "sourcesContent": ["\n'use client';\n\nimport { motion } from 'framer-motion';\n\nconst About = () => {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { opacity: 1, y: 0 }\n  };\n\n  return (\n    <section id=\"about\" className=\"bg-github-light py-20\">\n      <motion.div\n        variants={containerVariants}\n        initial=\"hidden\"\n        whileInView=\"visible\"\n        viewport={{ once: true, amount: 0.2 }}\n        className=\"section-container\"\n      >\n        <motion.h2 variants={itemVariants} className=\"section-title\">\n          About Me\n        </motion.h2>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          <motion.div variants={itemVariants} className=\"md:col-span-2 space-y-4\">\n            <p className=\"text-lg\">\n              🚀 I'm currently working on a photo-sharing platform with face recognition.\n            </p>\n            <p className=\"text-lg\">\n              👐 Open to contributing to the open-source community.\n            </p>\n            <p className=\"text-lg\">\n              🧠 Learning Machine Learning for face detection.\n            </p>\n            <p className=\"text-lg\">\n              💻 Passionate developer and open-source contributor.\n            </p>\n            <p className=\"text-lg\">\n              ⚡ Fun fact: I can spend hours debugging code but still forget where I kept my phone! 😄\n            </p>\n\n            <div className=\"pt-4\">\n              <h3 className=\"text-xl font-semibold mb-2\">Call to Action:</h3>\n              <p className=\"text-lg\">\n                Feel free to reach out if you'd like to collaborate, discuss tech, or share some awesome ideas!\n              </p>\n            </div>\n          </motion.div>\n\n          <motion.div variants={itemVariants} className=\"md:col-span-1\">\n            <div className=\"bg-github-dark border border-github-border rounded-2xl overflow-hidden card-hover\">\n              <div className=\"aspect-square w-full relative overflow-hidden\">\n                <div className=\"absolute inset-0 bg-gradient-to-br from-neon-green/20 to-neon-purple/20 z-10\"></div>\n                <img\n                  src=\"https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=MnwxfDB8MXxyYW5kb218MHx8dGVjaHx8fHx8fDE2MjM2MzYyODE&ixlib=rb-1.2.1&q=80&utm_campaign=api-credit&utm_medium=referral&utm_source=unsplash_source&w=1080\"\n                  alt=\"Code on screen\"\n                  className=\"w-full h-full object-cover object-center\"\n                />\n                <div className=\"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-github-dark to-transparent h-1/3 z-20\"></div>\n              </div>\n              <div className=\"p-6\">\n                <h3 className=\"text-xl font-bold mb-2\">Socials:</h3>\n                <div className=\"space-y-2\">\n                  <a\n                    href=\"https://instagram.com\"\n                    className=\"flex items-center space-x-2 text-github-text hover:text-neon-pink transition-colors\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                  >\n                    <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path d=\"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\"/>\n                    </svg>\n                    <span>Instagram</span>\n                  </a>\n                  <a\n                    href=\"https://linkedin.com\"\n                    className=\"flex items-center space-x-2 text-github-text hover:text-neon-blue transition-colors\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                  >\n                    <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n                    </svg>\n                    <span>LinkedIn</span>\n                  </a>\n                  <a\n                    href=\"mailto:<EMAIL>\"\n                    className=\"flex items-center space-x-2 text-github-text hover:text-white transition-colors\"\n                  >\n                    <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"></path>\n                    </svg>\n                    <span>Email</span>\n                  </a>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </motion.div>\n    </section>\n  );\n};\n\nexport default About;\n"], "names": [], "mappings": ";;;;AAGA;AAFA;;;AAIA,MAAM,QAAQ;IACZ,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC5B,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,UAAU;YACV,SAAQ;YACR,aAAY;YACZ,UAAU;gBAAE,MAAM;gBAAM,QAAQ;YAAI;YACpC,WAAU;;8BAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oBAAC,UAAU;oBAAc,WAAU;8BAAgB;;;;;;8BAI7D,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;;8CAC5C,8OAAC;oCAAE,WAAU;8CAAU;;;;;;8CAGvB,8OAAC;oCAAE,WAAU;8CAAU;;;;;;8CAGvB,8OAAC;oCAAE,WAAU;8CAAU;;;;;;8CAGvB,8OAAC;oCAAE,WAAU;8CAAU;;;;;;8CAGvB,8OAAC;oCAAE,WAAU;8CAAU;;;;;;8CAIvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;sDAAU;;;;;;;;;;;;;;;;;;sCAM3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;sCAC5C,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;0DAEZ,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAyB;;;;;;0DACvC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,WAAU;wDACV,QAAO;wDACP,KAAI;;0EAEJ,8OAAC;gEAAI,WAAU;gEAAU,MAAK;gEAAe,SAAQ;gEAAY,OAAM;0EACrE,cAAA,8OAAC;oEAAK,GAAE;;;;;;;;;;;0EAEV,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDACC,MAAK;wDACL,WAAU;wDACV,QAAO;wDACP,KAAI;;0EAEJ,8OAAC;gEAAI,WAAU;gEAAU,MAAK;gEAAe,SAAQ;gEAAY,OAAM;0EACrE,cAAA,8OAAC;oEAAK,GAAE;;;;;;;;;;;0EAEV,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDACC,MAAK;wDACL,WAAU;;0EAEV,8OAAC;gEAAI,WAAU;gEAAU,MAAK;gEAAO,QAAO;gEAAe,SAAQ;gEAAY,OAAM;0EACnF,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;0EAEvE,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1B;uCAEe", "debugId": null}}, {"offset": {"line": 1629, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/utils/animation.ts"], "sourcesContent": ["\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\nimport { ScrollToPlugin } from 'gsap/ScrollToPlugin';\n\n// Register GSAP plugins only on client side\nif (typeof window !== 'undefined') {\n  gsap.registerPlugin(ScrollTrigger, ScrollToPlugin);\n}\n\n// Initialize smooth scrolling with GSAP\nexport const initSmoothScrolling = () => {\n  if (typeof window === 'undefined') return;\n\n  // Add smooth scrolling to all anchor links\n  document.querySelectorAll('a[href^=\"#\"]').forEach(anchor => {\n    anchor.addEventListener('click', function (e) {\n      e.preventDefault();\n\n      const target = document.querySelector(this.getAttribute('href') || '');\n      if (target) {\n        gsap.to(window, {\n          duration: 1,\n          scrollTo: { y: target, offsetY: 80 },\n          ease: 'power3.inOut'\n        });\n      }\n    });\n  });\n};\n\n// Initialize scroll animations for various elements\nexport const initScrollAnimations = () => {\n  if (typeof window === 'undefined') return;\n\n  // Animate section headings on scroll\n  gsap.utils.toArray('.section-title').forEach((heading: any) => {\n    gsap.fromTo(\n      heading,\n      { y: 50, opacity: 0 },\n      {\n        y: 0,\n        opacity: 1,\n        duration: 0.6,\n        ease: 'power3.out',\n        scrollTrigger: {\n          trigger: heading,\n          start: 'top bottom-=100',\n          toggleActions: 'play none none none'\n        }\n      }\n    );\n  });\n\n  // Staggered animation for skills items\n  gsap.utils.toArray('.skills-grid').forEach((grid: any) => {\n    const items = gsap.utils.toArray('.skill-item', grid);\n\n    gsap.fromTo(\n      items,\n      { y: 30, opacity: 0 },\n      {\n        y: 0,\n        opacity: 1,\n        stagger: 0.05,\n        duration: 0.5,\n        ease: 'power3.out',\n        scrollTrigger: {\n          trigger: grid,\n          start: 'top bottom-=50',\n          toggleActions: 'play none none none'\n        }\n      }\n    );\n  });\n\n  // Project cards animation\n  gsap.utils.toArray('.project-card').forEach((card: any) => {\n    gsap.fromTo(\n      card,\n      { y: 40, opacity: 0 },\n      {\n        y: 0,\n        opacity: 1,\n        duration: 0.6,\n        ease: 'power2.out',\n        scrollTrigger: {\n          trigger: card,\n          start: 'top bottom-=50',\n          toggleActions: 'play none none none'\n        }\n      }\n    );\n  });\n};\n\n// Matrix-like terminal effect for text\nexport const terminalTextEffect = (element: HTMLElement, text: string, speed: number = 30) => {\n  let i = 0;\n  element.innerHTML = '';\n\n  const typeNextChar = () => {\n    if (i < text.length) {\n      element.innerHTML += text.charAt(i);\n      i++;\n      setTimeout(typeNextChar, speed);\n    }\n  };\n\n  typeNextChar();\n};\n\n// Reveal animation for GitHub contributions graph\nexport const animateGithubGraph = () => {\n  if (typeof window === 'undefined') return;\n\n  const cells = document.querySelectorAll('.github-cell');\n\n  gsap.fromTo(\n    cells,\n    { opacity: 0, scale: 0.8 },\n    {\n      opacity: 1,\n      scale: 1,\n      stagger: {\n        grid: [7, 52],\n        from: \"start\",\n        amount: 1.5\n      },\n      ease: 'power2.out',\n      scrollTrigger: {\n        trigger: '.github-graph',\n        start: 'top bottom-=100'\n      }\n    }\n  );\n};\n\n// Neon flicker effect for hero text\nexport const neonFlickerEffect = (element: HTMLElement) => {\n  const timeline = gsap.timeline({ repeat: -1, repeatDelay: 5 });\n\n  timeline\n    .to(element, { textShadow: '0 0 10px rgba(63, 185, 80, 0.8), 0 0 20px rgba(63, 185, 80, 0.5)', duration: 0.1 })\n    .to(element, { textShadow: 'none', duration: 0.1 })\n    .to(element, { textShadow: '0 0 10px rgba(63, 185, 80, 0.8), 0 0 20px rgba(63, 185, 80, 0.5)', duration: 0.1 })\n    .to(element, { textShadow: 'none', duration: 0.1 })\n    .to(element, { textShadow: '0 0 10px rgba(63, 185, 80, 0.8), 0 0 20px rgba(63, 185, 80, 0.5)', duration: 0.1 });\n\n  return timeline;\n};\n\n// Parallax effect for sections\nexport const createParallaxEffect = () => {\n  if (typeof window === 'undefined') return;\n\n  gsap.utils.toArray('section').forEach((section: any) => {\n    const bg = section.querySelector('.section-bg');\n    if (bg) {\n      gsap.to(bg, {\n        y: '30%',\n        ease: 'none',\n        scrollTrigger: {\n          trigger: section,\n          start: 'top bottom',\n          end: 'bottom top',\n          scrub: true\n        }\n      });\n    }\n  });\n};\n\n// Fix for skill hover selectors to avoid CSS selector issues\nexport const handleSkillHover = (element: HTMLElement, isEntering: boolean) => {\n  if (isEntering) {\n    gsap.to(element, {\n      y: -5,\n      boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.2)',\n      duration: 0.3,\n      ease: 'power2.out'\n    });\n  } else {\n    gsap.to(element, {\n      y: 0,\n      boxShadow: 'none',\n      duration: 0.3,\n      ease: 'power2.in'\n    });\n  }\n};\n\n// Project card hover effect\nexport const projectCardHover = (element: HTMLElement, isEntering: boolean) => {\n  if (isEntering) {\n    gsap.to(element, {\n      y: -8,\n      scale: 1.02,\n      boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.3)',\n      duration: 0.4,\n      ease: 'power2.out'\n    });\n  } else {\n    gsap.to(element, {\n      y: 0,\n      scale: 1,\n      boxShadow: 'none',\n      duration: 0.4,\n      ease: 'power2.in'\n    });\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;AACA;;;;AAIA,4CAA4C;AAC5C,uCAAmC;;AAEnC;AAGO,MAAM,sBAAsB;IACjC,wCAAmC;;AAiBrC;AAGO,MAAM,uBAAuB;IAClC,wCAAmC;;AA6DrC;AAGO,MAAM,qBAAqB,CAAC,SAAsB,MAAc,QAAgB,EAAE;IACvF,IAAI,IAAI;IACR,QAAQ,SAAS,GAAG;IAEpB,MAAM,eAAe;QACnB,IAAI,IAAI,KAAK,MAAM,EAAE;YACnB,QAAQ,SAAS,IAAI,KAAK,MAAM,CAAC;YACjC;YACA,WAAW,cAAc;QAC3B;IACF;IAEA;AACF;AAGO,MAAM,qBAAqB;IAChC,wCAAmC;;IAEnC,MAAM;AAoBR;AAGO,MAAM,oBAAoB,CAAC;IAChC,MAAM,WAAW,6IAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;QAAE,QAAQ,CAAC;QAAG,aAAa;IAAE;IAE5D,SACG,EAAE,CAAC,SAAS;QAAE,YAAY;QAAoE,UAAU;IAAI,GAC5G,EAAE,CAAC,SAAS;QAAE,YAAY;QAAQ,UAAU;IAAI,GAChD,EAAE,CAAC,SAAS;QAAE,YAAY;QAAoE,UAAU;IAAI,GAC5G,EAAE,CAAC,SAAS;QAAE,YAAY;QAAQ,UAAU;IAAI,GAChD,EAAE,CAAC,SAAS;QAAE,YAAY;QAAoE,UAAU;IAAI;IAE/G,OAAO;AACT;AAGO,MAAM,uBAAuB;IAClC,wCAAmC;;AAiBrC;AAGO,MAAM,mBAAmB,CAAC,SAAsB;IACrD,IAAI,YAAY;QACd,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,SAAS;YACf,GAAG,CAAC;YACJ,WAAW;YACX,UAAU;YACV,MAAM;QACR;IACF,OAAO;QACL,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,SAAS;YACf,GAAG;YACH,WAAW;YACX,UAAU;YACV,MAAM;QACR;IACF;AACF;AAGO,MAAM,mBAAmB,CAAC,SAAsB;IACrD,IAAI,YAAY;QACd,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,SAAS;YACf,GAAG,CAAC;YACJ,OAAO;YACP,WAAW;YACX,UAAU;YACV,MAAM;QACR;IACF,OAAO;QACL,6IAAA,CAAA,OAAI,CAAC,EAAE,CAAC,SAAS;YACf,GAAG;YACH,OAAO;YACP,WAAW;YACX,UAAU;YACV,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 1746, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/utils/dataUtils.ts"], "sourcesContent": ["\nimport data from '../data/data.json';\n\nexport const getSkillsData = () => {\n  return data.skills;\n};\n\nexport const getProjectsData = () => {\n  return data.projects;\n};\n"], "names": [], "mappings": ";;;;AACA;;AAEO,MAAM,gBAAgB;IAC3B,OAAO,2FAAA,CAAA,UAAI,CAAC,MAAM;AACpB;AAEO,MAAM,kBAAkB;IAC7B,OAAO,2FAAA,CAAA,UAAI,CAAC,QAAQ;AACtB", "debugId": null}}, {"offset": {"line": 1764, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/skills/DisplayToggle.tsx"], "sourcesContent": ["\n\n\ntype DisplayToggleProps = {\n  displayStyle: 'tabs' | 'keyboard';\n  toggleDisplayStyle: () => void;\n};\n\nconst DisplayToggle = ({ displayStyle, toggleDisplayStyle }: DisplayToggleProps) => {\n  // Determine button text based on current display style\n  const getButtonText = () => {\n    switch (displayStyle) {\n      case 'tabs':\n        return 'Switch to Keyboard View';\n      case 'keyboard':\n        return 'Switch to Tabs View';\n      default:\n        return 'Switch View';\n    }\n  };\n\n  return (\n    <div className=\"flex justify-end mb-6\">\n      <button\n        onClick={toggleDisplayStyle}\n        className=\"text-sm bg-github-light/30 px-4 py-2 rounded-md text-neon-green hover:bg-github-light/50 transition-colors\"\n      >\n        {getButtonText()}\n      </button>\n    </div>\n  );\n};\n\nexport default DisplayToggle;\n"], "names": [], "mappings": ";;;;;AAQA,MAAM,gBAAgB,CAAC,EAAE,YAAY,EAAE,kBAAkB,EAAsB;IAC7E,uDAAuD;IACvD,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YACC,SAAS;YACT,WAAU;sBAET;;;;;;;;;;;AAIT;uCAEe", "debugId": null}}, {"offset": {"line": 1805, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/ui/tabs.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1857, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/SkillIcon.tsx"], "sourcesContent": ["\nimport { HTMLAttributes } from 'react';\nimport * as icons from 'lucide-react';\n\ninterface SkillIconProps extends HTMLAttributes<HTMLDivElement> {\n  name: string;\n  color?: string;\n}\n\n// Type for the icon mapping\ntype IconName = keyof typeof icons;\n\nconst SkillIcon = ({ name, color, ...props }: SkillIconProps) => {\n  // Map of skill names to corresponding Lucide icons with correct casing\n  const iconMap: Record<string, IconName> = {\n    // Programming Languages\n    \"C++\": \"Code2\",\n    \"DART\": \"Code\",\n    \"JAVASCRIPT\": \"Code\",\n    \"PYTHON\": \"FileCode\",\n    \"TYPESCRIPT\": \"Code\",\n    \"RUST\": \"FileCode\",\n    \"POWERSHELL\": \"Terminal\",\n    \"BASH SCRIPT\": \"Terminal\",\n    \n    // Frontend\n    \"HTML5\": \"Code\",\n    \"CSS3\": \"Code\",\n    \"REACT\": \"Code\",\n    \"REACT NATIVE\": \"Code\",\n    \"ANGULAR\": \"Code\",\n    \"VUE.JS\": \"Code\",\n    \"BOOTSTRAP\": \"LayoutDashboard\",\n    \"TAILWINDCSS\": \"Code\",\n    \"NEXT\": \"Code\",\n    \"IONIC\": \"Code\",\n    \n    // Backend\n    \"NODE.JS\": \"Server\",\n    \"EXPRESS.JS\": \"Server\",\n    \"DJANGO\": \"Server\",\n    \"FLASK\": \"Server\",\n    \"FASTAPI\": \"Server\",\n    \"SPRING\": \"Server\",\n    \n    // Cloud & Deployment\n    \"AWS\": \"Cloud\",\n    \"AZURE\": \"Cloud\",\n    \"FIREBASE\": \"Database\",\n    \"GOOGLECLOUD\": \"Cloud\",\n    \"NETLIFY\": \"Cloud\",\n    \"RENDER\": \"Cloud\",\n    \"VERCEL\": \"Cloud\",\n    \n    // Databases\n    \"MYSQL\": \"Database\",\n    \"SQLITE\": \"Database\",\n    \"MONGODB\": \"Database\",\n    \"SUPABASE\": \"Database\",\n    \n    // DevOps & Tools\n    \"GITHUB ACTIONS\": \"Github\",\n    \"GIT\": \"Github\",\n    \"DOCKER\": \"Code\",\n    \"POSTMAN\": \"Code\",\n    \"KUBERNETES\": \"Code\",\n    \"GITHUB\": \"Github\",\n    \n    // Data Science & ML\n    \"MATPLOTLIB\": \"ChartBar\",\n    \"NUMPY\": \"Table\",\n    \"PANDAS\": \"Table\",\n    \"TENSORFLOW\": \"Code\",\n    \"PYTORCH\": \"Code\",\n    \n    // UI/UX & Design\n    \"FIGMA\": \"Code\",\n    \"CANVA\": \"Image\",\n    \"BLENDER\": \"Code\",\n    \"ADOBE CREATIVE CLOUD\": \"Image\",\n    \"ADOBE PHOTOSHOP\": \"Image\"\n  };\n\n  // Get the icon component - ensure it's a valid icon, defaulting to Code if not found\n  const iconName = iconMap[name] || \"Code\";\n  \n  // Type assertion to ensure TypeScript knows this is a valid component\n  const IconComponent = icons[iconName] as React.ComponentType<{ size?: number, color?: string, className?: string }>;\n\n  // Generate a default color if not provided\n  const iconColor = color || \"#c9d1d9\";\n\n  return (\n    <div \n      className=\"skill-icon flex items-center justify-center p-2 rounded-md bg-github-darker\"\n      {...props}\n    >\n      <IconComponent size={24} color={iconColor} className=\"skill-icon-svg\" />\n    </div>\n  );\n};\n\nexport default SkillIcon;\n"], "names": [], "mappings": ";;;;AAEA;;;AAUA,MAAM,YAAY,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAuB;IAC1D,uEAAuE;IACvE,MAAM,UAAoC;QACxC,wBAAwB;QACxB,OAAO;QACP,QAAQ;QACR,cAAc;QACd,UAAU;QACV,cAAc;QACd,QAAQ;QACR,cAAc;QACd,eAAe;QAEf,WAAW;QACX,SAAS;QACT,QAAQ;QACR,SAAS;QACT,gBAAgB;QAChB,WAAW;QACX,UAAU;QACV,aAAa;QACb,eAAe;QACf,QAAQ;QACR,SAAS;QAET,UAAU;QACV,WAAW;QACX,cAAc;QACd,UAAU;QACV,SAAS;QACT,WAAW;QACX,UAAU;QAEV,qBAAqB;QACrB,OAAO;QACP,SAAS;QACT,YAAY;QACZ,eAAe;QACf,WAAW;QACX,UAAU;QACV,UAAU;QAEV,YAAY;QACZ,SAAS;QACT,UAAU;QACV,WAAW;QACX,YAAY;QAEZ,iBAAiB;QACjB,kBAAkB;QAClB,OAAO;QACP,UAAU;QACV,WAAW;QACX,cAAc;QACd,UAAU;QAEV,oBAAoB;QACpB,cAAc;QACd,SAAS;QACT,UAAU;QACV,cAAc;QACd,WAAW;QAEX,iBAAiB;QACjB,SAAS;QACT,SAAS;QACT,WAAW;QACX,wBAAwB;QACxB,mBAAmB;IACrB;IAEA,qFAAqF;IACrF,MAAM,WAAW,OAAO,CAAC,KAAK,IAAI;IAElC,sEAAsE;IACtE,MAAM,gBAAgB,iKAAK,CAAC,SAAS;IAErC,2CAA2C;IAC3C,MAAM,YAAY,SAAS;IAE3B,qBACE,8OAAC;QACC,WAAU;QACT,GAAG,KAAK;kBAET,cAAA,8OAAC;YAAc,MAAM;YAAI,OAAO;YAAW,WAAU;;;;;;;;;;;AAG3D;uCAEe", "debugId": null}}, {"offset": {"line": 1958, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/skills/SkillItem.tsx"], "sourcesContent": ["\nimport { motion } from 'framer-motion';\nimport SkillIcon from '../SkillIcon';\n\ntype SkillItemProps = {\n  skill: {\n    name: string;\n    color: string;\n    level: number;\n  };\n  index: number;\n  isHovered: boolean;\n  onHover: (element: HTMLElement, skill: string, isEntering: boolean) => void;\n};\n\nconst SkillItem = ({ skill, index, isHovered, onHover }: SkillItemProps) => {\n  // Create a safe id that can be used with element selectors\n  const safeId = `skill-${skill.name.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}-${index}`;\n  \n  return (\n    <motion.div\n      id={safeId}\n      initial={{ opacity: 0, y: 20 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5, delay: index * 0.05 }}\n      viewport={{ once: true }}\n      className=\"bg-github-light rounded-lg p-4 border border-github-border skill-item transition-all duration-300\"\n      onMouseEnter={(e) => onHover(e.currentTarget, skill.name, true)}\n      onMouseLeave={(e) => onHover(e.currentTarget, skill.name, false)}\n    >\n      <div className=\"flex items-center gap-3 mb-3\">\n        <SkillIcon \n          name={skill.name} \n          color={isHovered ? \"#3fb950\" : undefined} \n        />\n        <div className=\"flex justify-between items-center w-full\">\n          <span className=\"text-white font-medium\">{skill.name}</span>\n          <span className=\"text-sm text-neon-green\">{skill.level}%</span>\n        </div>\n      </div>\n      <div className=\"w-full bg-github-dark rounded-full h-2.5\">\n        <motion.div \n          initial={{ width: 0 }}\n          whileInView={{ width: `${skill.level}%` }}\n          transition={{ duration: 1, ease: \"easeOut\" }}\n          viewport={{ once: true }}\n          className={`h-2.5 rounded-full ${skill.color}`}\n        ></motion.div>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default SkillItem;\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAaA,MAAM,YAAY,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAkB;IACrE,2DAA2D;IAC3D,MAAM,SAAS,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC,OAAO,CAAC,iBAAiB,KAAK,WAAW,GAAG,CAAC,EAAE,OAAO;IAEzF,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,IAAI;QACJ,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK,OAAO,QAAQ;QAAK;QACjD,UAAU;YAAE,MAAM;QAAK;QACvB,WAAU;QACV,cAAc,CAAC,IAAM,QAAQ,EAAE,aAAa,EAAE,MAAM,IAAI,EAAE;QAC1D,cAAc,CAAC,IAAM,QAAQ,EAAE,aAAa,EAAE,MAAM,IAAI,EAAE;;0BAE1D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,2IAAA,CAAA,UAAS;wBACR,MAAM,MAAM,IAAI;wBAChB,OAAO,YAAY,YAAY;;;;;;kCAEjC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAA0B,MAAM,IAAI;;;;;;0CACpD,8OAAC;gCAAK,WAAU;;oCAA2B,MAAM,KAAK;oCAAC;;;;;;;;;;;;;;;;;;;0BAG3D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,OAAO;oBAAE;oBACpB,aAAa;wBAAE,OAAO,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC;oBAAC;oBACxC,YAAY;wBAAE,UAAU;wBAAG,MAAM;oBAAU;oBAC3C,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAW,CAAC,mBAAmB,EAAE,MAAM,KAAK,EAAE;;;;;;;;;;;;;;;;;AAKxD;uCAEe", "debugId": null}}, {"offset": {"line": 2077, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/skills/SkillCategory.tsx"], "sourcesContent": ["\nimport { motion } from 'framer-motion';\nimport SkillItem from './SkillItem';\n\ntype Skill = {\n  name: string;\n  color: string;\n  level: number;\n};\n\ntype SkillCategoryProps = {\n  category: {\n    name: string;\n    description: string;\n    skills: Skill[];\n  };\n  hoveredSkill: string | null;\n  onSkillHover: (element: HTMLElement, skill: string, isEntering: boolean) => void;\n};\n\nconst SkillCategory = ({ category, hoveredSkill, onSkillHover }: SkillCategoryProps) => {\n  return (\n    <>\n      <div className=\"bg-github-light/20 rounded-lg p-4 mb-6\">\n        <h3 className=\"text-xl text-white font-medium mb-2\">\n          {category.name}\n        </h3>\n        <p className=\"text-github-text\">\n          {category.description}\n        </p>\n      </div>\n      \n      <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 skills-grid\">\n        {category.skills.map((skill, index) => (\n          <SkillItem \n            key={`${skill.name}-${index}`}\n            skill={skill}\n            index={index}\n            isHovered={hoveredSkill === skill.name}\n            onHover={onSkillHover}\n          />\n        ))}\n      </div>\n    </>\n  );\n};\n\nexport default SkillCategory;\n"], "names": [], "mappings": ";;;;AAEA;;;AAkBA,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAE,YAAY,EAAE,YAAY,EAAsB;IACjF,qBACE;;0BACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACX,SAAS,IAAI;;;;;;kCAEhB,8OAAC;wBAAE,WAAU;kCACV,SAAS,WAAW;;;;;;;;;;;;0BAIzB,8OAAC;gBAAI,WAAU;0BACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC3B,8OAAC,qJAAA,CAAA,UAAS;wBAER,OAAO;wBACP,OAAO;wBACP,WAAW,iBAAiB,MAAM,IAAI;wBACtC,SAAS;uBAJJ,GAAG,MAAM,IAAI,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;;;AAUzC;uCAEe", "debugId": null}}, {"offset": {"line": 2139, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/skills/TopSkills.tsx"], "sourcesContent": ["\nimport { motion } from 'framer-motion';\nimport SkillIcon from '../SkillIcon';\n\ntype TopSkillProps = {\n  skills: { name: string; level: number }[];\n  onSkillHover: (element: HTMLElement, skill: string, isEntering: boolean) => void;\n};\n\nconst TopSkills = ({ skills, onSkillHover }: TopSkillProps) => {\n  return (\n    <div className=\"mt-12\">\n      <h3 className=\"text-xl text-white font-bold mb-4\">Top Skills at a Glance</h3>\n      <div className=\"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4\">\n        {skills.map((skill, index) => {\n          const safeId = `top-skill-${skill.name.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}-${index}`;\n          return (\n            <motion.div\n              key={safeId}\n              id={safeId}\n              initial={{ opacity: 0, scale: 0.9 }}\n              whileInView={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.5, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"bg-github-light/30 p-3 rounded-lg text-center flex flex-col items-center transition-all duration-300 hover:bg-github-light/50\"\n              onMouseEnter={(e) => onSkillHover(e.currentTarget, skill.name, true)}\n              onMouseLeave={(e) => onSkillHover(e.currentTarget, skill.name, false)}\n            >\n              <SkillIcon name={skill.name} color=\"#3fb950\" />\n              <span className=\"text-white mt-2\">{skill.name}</span>\n              <span className=\"text-neon-green text-sm\">{skill.level}%</span>\n            </motion.div>\n          );\n        })}\n      </div>\n    </div>\n  );\n};\n\nexport default TopSkills;\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAOA,MAAM,YAAY,CAAC,EAAE,MAAM,EAAE,YAAY,EAAiB;IACxD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAoC;;;;;;0BAClD,8OAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,OAAO;oBAClB,MAAM,SAAS,CAAC,UAAU,EAAE,MAAM,IAAI,CAAC,OAAO,CAAC,iBAAiB,KAAK,WAAW,GAAG,CAAC,EAAE,OAAO;oBAC7F,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,IAAI;wBACJ,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,aAAa;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBACpC,YAAY;4BAAE,UAAU;4BAAK,OAAO,QAAQ;wBAAI;wBAChD,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;wBACV,cAAc,CAAC,IAAM,aAAa,EAAE,aAAa,EAAE,MAAM,IAAI,EAAE;wBAC/D,cAAc,CAAC,IAAM,aAAa,EAAE,aAAa,EAAE,MAAM,IAAI,EAAE;;0CAE/D,8OAAC,2IAAA,CAAA,UAAS;gCAAC,MAAM,MAAM,IAAI;gCAAE,OAAM;;;;;;0CACnC,8OAAC;gCAAK,WAAU;0CAAmB,MAAM,IAAI;;;;;;0CAC7C,8OAAC;gCAAK,WAAU;;oCAA2B,MAAM,KAAK;oCAAC;;;;;;;;uBAZlD;;;;;gBAeX;;;;;;;;;;;;AAIR;uCAEe", "debugId": null}}, {"offset": {"line": 2238, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/skills/TabSkillsView.tsx"], "sourcesContent": ["\nimport { motion } from 'framer-motion';\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON><PERSON>ontent } from \"../../ui/tabs\";\nimport SkillCategory from './SkillCategory';\nimport TopSkills from './TopSkills';\n\ntype TabSkillsViewProps = {\n  categories: {\n    name: string;\n    description: string;\n    skills: {\n      name: string;\n      color: string;\n      level: number;\n    }[];\n  }[];\n  topSkills: { name: string; level: number }[];\n  hoveredSkill: string | null;\n  onSkillHover: (element: HTMLElement, skill: string, isEntering: boolean) => void;\n};\n\nconst TabSkillsView = ({ \n  categories,\n  topSkills,\n  hoveredSkill,\n  onSkillHover\n}: TabSkillsViewProps) => {\n  return (\n    <>\n      <div className=\"mb-10\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.2 }}\n          viewport={{ once: true }}\n        >\n          <Tabs defaultValue={categories[0].name} className=\"w-full\">\n            <TabsList className=\"flex flex-wrap mb-6 bg-github-light/20\">\n              {categories.map((category) => (\n                <TabsTrigger\n                  key={category.name}\n                  value={category.name}\n                  className=\"data-[state=active]:bg-neon-green data-[state=active]:text-black\"\n                >\n                  {category.name}\n                </TabsTrigger>\n              ))}\n            </TabsList>\n            \n            {categories.map((category) => (\n              <TabsContent key={category.name} value={category.name}>\n                <SkillCategory \n                  category={category}\n                  hoveredSkill={hoveredSkill}\n                  onSkillHover={onSkillHover}\n                />\n              </TabsContent>\n            ))}\n          </Tabs>\n        </motion.div>\n      </div>\n      \n      <TopSkills skills={topSkills} onSkillHover={onSkillHover} />\n    </>\n  );\n};\n\nexport default TabSkillsView;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;;;;;;AAiBA,MAAM,gBAAgB,CAAC,EACrB,UAAU,EACV,SAAS,EACT,YAAY,EACZ,YAAY,EACO;IACnB,qBACE;;0BACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;8BAEvB,cAAA,8OAAC,gIAAA,CAAA,OAAI;wBAAC,cAAc,UAAU,CAAC,EAAE,CAAC,IAAI;wBAAE,WAAU;;0CAChD,8OAAC,gIAAA,CAAA,WAAQ;gCAAC,WAAU;0CACjB,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,gIAAA,CAAA,cAAW;wCAEV,OAAO,SAAS,IAAI;wCACpB,WAAU;kDAET,SAAS,IAAI;uCAJT,SAAS,IAAI;;;;;;;;;;4BASvB,WAAW,GAAG,CAAC,CAAC,yBACf,8OAAC,gIAAA,CAAA,cAAW;oCAAqB,OAAO,SAAS,IAAI;8CACnD,cAAA,8OAAC,yJAAA,CAAA,UAAa;wCACZ,UAAU;wCACV,cAAc;wCACd,cAAc;;;;;;mCAJA,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;0BAYvC,8OAAC,qJAAA,CAAA,UAAS;gBAAC,QAAQ;gBAAW,cAAc;;;;;;;;AAGlD;uCAEe", "debugId": null}}, {"offset": {"line": 2342, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/ui/use-toast.ts"], "sourcesContent": ["import { useToast, toast } from \"@/hooks/use-toast\";\n\nexport { useToast, toast };\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 2361, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/data/skillsData.ts"], "sourcesContent": ["// Skills data for the keyboard component\nexport interface Skill {\n  id: string;\n  name: string;\n  description: string;\n  logo: string;\n  color: string;\n  experience: number;\n  proficiency: number;\n  projects: string[];\n  strengths: string[];\n  category: SkillCategory;\n}\n\nexport type SkillCategory = \n  | 'frontend' \n  | 'backend' \n  | 'language' \n  | 'database' \n  | 'devops' \n  | 'mobile' \n  | 'design' \n  | 'other';\n\n// Skill data with detailed information\nexport const skills: Skill[] = [\n  {\n    id: 'js',\n    name: 'JavaScript',\n    description: 'A versatile scripting language that conforms to the ECMAScript specification.',\n    logo: 'javascript',\n    color: '#F7DF1E',\n    experience: 5,\n    proficiency: 95,\n    projects: ['Portfolio Website', 'E-commerce Platform', 'Social Media Dashboard'],\n    strengths: ['ES6+', 'Async/Await', 'DOM Manipulation', 'Functional Programming'],\n    category: 'language'\n  },\n  {\n    id: 'ts',\n    name: 'TypeScript',\n    description: 'A strongly typed programming language that builds on JavaScript.',\n    logo: 'typescript',\n    color: '#3178C6',\n    experience: 3,\n    proficiency: 90,\n    projects: ['Enterprise CRM', 'Financial Dashboard', 'API Gateway'],\n    strengths: ['Type Safety', 'Interface Design', 'Generics', 'Advanced Types'],\n    category: 'language'\n  },\n  {\n    id: 'react',\n    name: 'React',\n    description: 'A JavaScript library for building user interfaces.',\n    logo: 'react',\n    color: '#61DAFB',\n    experience: 4,\n    proficiency: 92,\n    projects: ['E-commerce Platform', 'Social Media Dashboard', 'Portfolio Website'],\n    strengths: ['Hooks', 'Context API', 'Custom Hooks', 'Performance Optimization'],\n    category: 'frontend'\n  },\n  {\n    id: 'node',\n    name: 'Node.js',\n    description: 'A JavaScript runtime built on Chrome\\'s V8 JavaScript engine.',\n    logo: 'nodejs',\n    color: '#339933',\n    experience: 4,\n    proficiency: 88,\n    projects: ['REST API Services', 'Real-time Chat Application', 'Data Processing Pipeline'],\n    strengths: ['Express.js', 'API Development', 'Async Programming', 'Performance Tuning'],\n    category: 'backend'\n  },\n  {\n    id: 'python',\n    name: 'Python',\n    description: 'A high-level, interpreted programming language with dynamic semantics.',\n    logo: 'python',\n    color: '#3776AB',\n    experience: 3,\n    proficiency: 85,\n    projects: ['Data Analysis Tool', 'Machine Learning Model', 'Automation Scripts'],\n    strengths: ['Data Processing', 'Machine Learning', 'Scripting', 'Web Scraping'],\n    category: 'language'\n  },\n  {\n    id: 'aws',\n    name: 'AWS',\n    description: 'A comprehensive cloud computing platform provided by Amazon.',\n    logo: 'aws',\n    color: '#FF9900',\n    experience: 3,\n    proficiency: 80,\n    projects: ['Serverless Application', 'Cloud Migration', 'Scalable Web Services'],\n    strengths: ['Lambda', 'S3', 'EC2', 'CloudFormation', 'DynamoDB'],\n    category: 'devops'\n  },\n  {\n    id: 'docker',\n    name: 'Docker',\n    description: 'A platform for developing, shipping, and running applications in containers.',\n    logo: 'docker',\n    color: '#2496ED',\n    experience: 3,\n    proficiency: 85,\n    projects: ['Microservices Architecture', 'CI/CD Pipeline', 'Development Environment'],\n    strengths: ['Containerization', 'Docker Compose', 'Multi-stage Builds', 'Optimization'],\n    category: 'devops'\n  },\n  {\n    id: 'mongodb',\n    name: 'MongoDB',\n    description: 'A cross-platform document-oriented database program.',\n    logo: 'mongodb',\n    color: '#47A248',\n    experience: 3,\n    proficiency: 82,\n    projects: ['E-commerce Platform', 'Content Management System', 'Analytics Dashboard'],\n    strengths: ['Schema Design', 'Aggregation Pipeline', 'Indexing', 'Performance Tuning'],\n    category: 'database'\n  },\n  {\n    id: 'postgres',\n    name: 'PostgreSQL',\n    description: 'A powerful, open source object-relational database system.',\n    logo: 'postgresql',\n    color: '#336791',\n    experience: 4,\n    proficiency: 88,\n    projects: ['Financial System', 'Inventory Management', 'Data Warehouse'],\n    strengths: ['Complex Queries', 'Performance Tuning', 'Data Integrity', 'JSON Support'],\n    category: 'database'\n  },\n  {\n    id: 'graphql',\n    name: 'GraphQL',\n    description: 'A query language for APIs and a runtime for executing those queries.',\n    logo: 'graphql',\n    color: '#E10098',\n    experience: 2,\n    proficiency: 78,\n    projects: ['API Gateway', 'Content Platform', 'Mobile App Backend'],\n    strengths: ['Schema Design', 'Resolvers', 'Type System', 'Query Optimization'],\n    category: 'backend'\n  },\n  {\n    id: 'vue',\n    name: 'Vue.js',\n    description: 'A progressive JavaScript framework for building user interfaces.',\n    logo: 'vuejs',\n    color: '#4FC08D',\n    experience: 2,\n    proficiency: 75,\n    projects: ['Admin Dashboard', 'E-commerce Frontend', 'Interactive Documentation'],\n    strengths: ['Component System', 'Reactivity', 'Vue Router', 'Vuex'],\n    category: 'frontend'\n  },\n  {\n    id: 'tailwind',\n    name: 'Tailwind CSS',\n    description: 'A utility-first CSS framework for rapidly building custom designs.',\n    logo: 'tailwindcss',\n    color: '#06B6D4',\n    experience: 3,\n    proficiency: 90,\n    projects: ['Portfolio Website', 'Marketing Site', 'Web Application UI'],\n    strengths: ['Rapid Prototyping', 'Responsive Design', 'Custom Theming', 'Dark Mode'],\n    category: 'frontend'\n  },\n  {\n    id: 'threejs',\n    name: 'Three.js',\n    description: 'A cross-browser JavaScript library used to create and display animated 3D computer graphics.',\n    logo: 'threejs',\n    color: '#000000',\n    experience: 2,\n    proficiency: 75,\n    projects: ['3D Portfolio', 'Interactive Product Viewer', 'Data Visualization'],\n    strengths: ['WebGL', '3D Modeling', 'Animation', 'Performance Optimization'],\n    category: 'frontend'\n  },\n  {\n    id: 'git',\n    name: 'Git',\n    description: 'A distributed version control system for tracking changes in source code.',\n    logo: 'git',\n    color: '#F05032',\n    experience: 5,\n    proficiency: 92,\n    projects: ['All Projects', 'Open Source Contributions', 'Team Collaboration'],\n    strengths: ['Branching Strategy', 'Conflict Resolution', 'Git Flow', 'Advanced Commands'],\n    category: 'devops'\n  },\n  {\n    id: 'nextjs',\n    name: 'Next.js',\n    description: 'A React framework with hybrid static & server rendering, TypeScript support, and route pre-fetching.',\n    logo: 'nextjs',\n    color: '#000000',\n    experience: 3,\n    proficiency: 85,\n    projects: ['E-commerce Platform', 'Corporate Website', 'Blog Platform'],\n    strengths: ['SSR/SSG', 'API Routes', 'Image Optimization', 'Incremental Static Regeneration'],\n    category: 'frontend'\n  }\n];\n\n// Map of skill IDs to their index in the skills array for quick lookup\nexport const skillsMap = skills.reduce((acc, skill, index) => {\n  acc[skill.id] = index;\n  return acc;\n}, {} as Record<string, number>);\n\n// Get skill by ID\nexport const getSkillById = (id: string): Skill | undefined => {\n  const index = skillsMap[id];\n  return typeof index === 'number' ? skills[index] : undefined;\n};\n\n// Get skills by category\nexport const getSkillsByCategory = (category: SkillCategory): Skill[] => {\n  return skills.filter(skill => skill.category === category);\n};\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;;;AAyBlC,MAAM,SAAkB;IAC7B;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,YAAY;QACZ,aAAa;QACb,UAAU;YAAC;YAAqB;YAAuB;SAAyB;QAChF,WAAW;YAAC;YAAQ;YAAe;YAAoB;SAAyB;QAChF,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,YAAY;QACZ,aAAa;QACb,UAAU;YAAC;YAAkB;YAAuB;SAAc;QAClE,WAAW;YAAC;YAAe;YAAoB;YAAY;SAAiB;QAC5E,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,YAAY;QACZ,aAAa;QACb,UAAU;YAAC;YAAuB;YAA0B;SAAoB;QAChF,WAAW;YAAC;YAAS;YAAe;YAAgB;SAA2B;QAC/E,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,YAAY;QACZ,aAAa;QACb,UAAU;YAAC;YAAqB;YAA8B;SAA2B;QACzF,WAAW;YAAC;YAAc;YAAmB;YAAqB;SAAqB;QACvF,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,YAAY;QACZ,aAAa;QACb,UAAU;YAAC;YAAsB;YAA0B;SAAqB;QAChF,WAAW;YAAC;YAAmB;YAAoB;YAAa;SAAe;QAC/E,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,YAAY;QACZ,aAAa;QACb,UAAU;YAAC;YAA0B;YAAmB;SAAwB;QAChF,WAAW;YAAC;YAAU;YAAM;YAAO;YAAkB;SAAW;QAChE,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,YAAY;QACZ,aAAa;QACb,UAAU;YAAC;YAA8B;YAAkB;SAA0B;QACrF,WAAW;YAAC;YAAoB;YAAkB;YAAsB;SAAe;QACvF,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,YAAY;QACZ,aAAa;QACb,UAAU;YAAC;YAAuB;YAA6B;SAAsB;QACrF,WAAW;YAAC;YAAiB;YAAwB;YAAY;SAAqB;QACtF,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,YAAY;QACZ,aAAa;QACb,UAAU;YAAC;YAAoB;YAAwB;SAAiB;QACxE,WAAW;YAAC;YAAmB;YAAsB;YAAkB;SAAe;QACtF,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,YAAY;QACZ,aAAa;QACb,UAAU;YAAC;YAAe;YAAoB;SAAqB;QACnE,WAAW;YAAC;YAAiB;YAAa;YAAe;SAAqB;QAC9E,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,YAAY;QACZ,aAAa;QACb,UAAU;YAAC;YAAmB;YAAuB;SAA4B;QACjF,WAAW;YAAC;YAAoB;YAAc;YAAc;SAAO;QACnE,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,YAAY;QACZ,aAAa;QACb,UAAU;YAAC;YAAqB;YAAkB;SAAqB;QACvE,WAAW;YAAC;YAAqB;YAAqB;YAAkB;SAAY;QACpF,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,YAAY;QACZ,aAAa;QACb,UAAU;YAAC;YAAgB;YAA8B;SAAqB;QAC9E,WAAW;YAAC;YAAS;YAAe;YAAa;SAA2B;QAC5E,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,YAAY;QACZ,aAAa;QACb,UAAU;YAAC;YAAgB;YAA6B;SAAqB;QAC7E,WAAW;YAAC;YAAsB;YAAuB;YAAY;SAAoB;QACzF,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,YAAY;QACZ,aAAa;QACb,UAAU;YAAC;YAAuB;YAAqB;SAAgB;QACvE,WAAW;YAAC;YAAW;YAAc;YAAsB;SAAkC;QAC7F,UAAU;IACZ;CACD;AAGM,MAAM,YAAY,OAAO,MAAM,CAAC,CAAC,KAAK,OAAO;IAClD,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG;IAChB,OAAO;AACT,GAAG,CAAC;AAGG,MAAM,eAAe,CAAC;IAC3B,MAAM,QAAQ,SAAS,CAAC,GAAG;IAC3B,OAAO,OAAO,UAAU,WAAW,MAAM,CAAC,MAAM,GAAG;AACrD;AAGO,MAAM,sBAAsB,CAAC;IAClC,OAAO,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;AACnD", "debugId": null}}, {"offset": {"line": 2703, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/skills/keyboard/LoadingScreen.tsx"], "sourcesContent": ["\n'use client';\n\n/**\n * Loading screen component for 3D scene\n */\nconst LoadingScreen = () => {\n  return (\n    <div className=\"absolute inset-0 flex flex-col items-center justify-center bg-github-dark/80 backdrop-blur-sm z-10\">\n      <div className=\"w-16 h-16 border-4 border-neon-green border-t-transparent rounded-full animate-spin mb-4\"></div>\n      <p className=\"text-white text-lg\">Loading Keyboard...</p>\n    </div>\n  );\n};\n\nexport default LoadingScreen;\n"], "names": [], "mappings": ";;;;AACA;;AAEA;;CAEC,GACD,MAAM,gBAAgB;IACpB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAE,WAAU;0BAAqB;;;;;;;;;;;;AAGxC;uCAEe", "debugId": null}}, {"offset": {"line": 2744, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/skills/KeyboardSkillsView.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { useState, useRef, useEffect } from 'react';\nimport { Skill } from '../../../data/skillsData';\nimport Spline from '@splinetool/react-spline';\nimport { Application } from '@splinetool/runtime';\nimport { toast } from '@/components/ui/use-toast';\nimport { getSkillById } from '../../../data/skillsData';\n// import { KEYBOARD_LAYOUT, getKeyByIdFixed } from '../../../data/keyboardData';\nimport LoadingScreen from './keyboard/LoadingScreen';\n\n// Skill card component to display when a key is pressed\ninterface SkillCardProps {\n  skill: Skill | null;\n  isVisible: boolean;\n  onClose: () => void;\n}\n\nconst SkillCard = ({ skill, isVisible, onClose }: SkillCardProps) => {\n  if (!skill) return null;\n\n  // Calculate experience level text\n  const getExperienceLevel = (years: number): string => {\n    if (years < 1) return 'Beginner';\n    if (years < 2) return 'Intermediate';\n    if (years < 4) return 'Advanced';\n    return 'Expert';\n  };\n\n  // Calculate proficiency color\n  const getProficiencyColor = (proficiency: number): string => {\n    if (proficiency < 60) return '#f97316'; // Orange\n    if (proficiency < 80) return '#22c55e'; // Green\n    return '#3b82f6'; // Blue\n  };\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20, scale: 0.95 }}\n      animate={{ opacity: 1, y: 0, scale: 1 }}\n      exit={{ opacity: 0, y: -20, scale: 0.95 }}\n      transition={{\n        type: 'spring',\n        stiffness: 300,\n        damping: 30,\n        duration: 0.4\n      }}\n      className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 bg-github-dark/95 border border-github-border rounded-lg p-5 w-full max-w-md z-50 shadow-xl\"\n      style={{ backdropFilter: 'blur(12px)' }}\n    >\n      {/* Close button */}\n      <button\n        onClick={onClose}\n        className=\"absolute top-3 right-3 text-github-text hover:text-white transition-colors\"\n        aria-label=\"Close skill details\"\n      >\n        <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n          <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\"></line>\n          <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\"></line>\n        </svg>\n      </button>\n\n      {/* Header with logo and name */}\n      <div className=\"flex items-center gap-4 mb-4\">\n        <div\n          className=\"w-16 h-16 rounded-lg flex items-center justify-center text-white text-2xl font-bold shadow-md\"\n          style={{ backgroundColor: skill.color }}\n        >\n          {skill.logo}\n        </div>\n        <div className=\"flex-1\">\n          <h3 className=\"text-2xl font-bold text-white mb-1\">{skill.name}</h3>\n          <div className=\"flex items-center gap-2\">\n            <span className=\"text-github-text font-medium\">{getExperienceLevel(skill.experience)}</span>\n            <span className=\"text-github-text text-sm\">•</span>\n            <span className=\"text-github-text\">{skill.experience} years</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Proficiency bar */}\n      <div className=\"mb-4\">\n        <div className=\"flex justify-between items-center mb-1\">\n          <span className=\"text-white font-medium\">Proficiency</span>\n          <span className=\"text-white font-bold\">{skill.proficiency}%</span>\n        </div>\n        <div className=\"h-3 bg-github-light/30 rounded-full overflow-hidden\">\n          <motion.div\n            initial={{ width: 0 }}\n            animate={{ width: `${skill.proficiency}%` }}\n            transition={{ duration: 1, ease: \"easeOut\" }}\n            className=\"h-full rounded-full\"\n            style={{ backgroundColor: getProficiencyColor(skill.proficiency) }}\n          />\n        </div>\n      </div>\n\n      {/* Description */}\n      <p className=\"text-github-text mb-4 leading-relaxed\">{skill.description}</p>\n\n      {/* Projects */}\n      {skill.projects && skill.projects.length > 0 && (\n        <div className=\"mb-4\">\n          <h4 className=\"text-white font-medium mb-2 flex items-center\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 mr-2\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n              <path d=\"M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z\"></path>\n            </svg>\n            Projects\n          </h4>\n          <ul className=\"space-y-1 text-github-text\">\n            {skill.projects.map((project, index) => (\n              <li key={index} className=\"flex items-start\">\n                <span className=\"text-xs mr-2 mt-1\">•</span>\n                {project}\n              </li>\n            ))}\n          </ul>\n        </div>\n      )}\n\n      {/* Key Strengths */}\n      {skill.strengths && skill.strengths.length > 0 && (\n        <div>\n          <h4 className=\"text-white font-medium mb-2 flex items-center\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4 mr-2\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n              <path d=\"M12 2L2 7l10 5 10-5-10-5z\"></path>\n              <path d=\"M2 17l10 5 10-5\"></path>\n              <path d=\"M2 12l10 5 10-5\"></path>\n            </svg>\n            Key Strengths\n          </h4>\n          <div className=\"flex flex-wrap gap-2\">\n            {skill.strengths.map((strength, index) => (\n              <motion.span\n                key={index}\n                initial={{ opacity: 0, scale: 0.8 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ delay: index * 0.1 }}\n                className=\"px-3 py-1 rounded-full text-sm font-medium\"\n                style={{\n                  backgroundColor: `${skill.color}22`,\n                  color: skill.color,\n                  border: `1px solid ${skill.color}44`\n                }}\n              >\n                {strength}\n              </motion.span>\n            ))}\n          </div>\n        </div>\n      )}\n    </motion.div>\n  );\n};\n\nconst KeyboardSkillsView = () => {\n  const [selectedSkill, setSelectedSkill] = useState<Skill | null>(null);\n  const [showSkillInfo, setShowSkillInfo] = useState<boolean>(false);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n  const splineRef = useRef<Application | null>(null);\n\n  // Handle skill selection when a key is pressed in the Spline scene\n  const onSplineLoad = (splineApp: Application) => {\n    splineRef.current = splineApp;\n    setLoading(false);\n\n    try {\n      // Set up interaction handlers for Spline objects\n      splineApp.addEventListener('mouseDown', (e) => {\n        if (e.target && e.target.name) {\n          handleKeyPress(e.target.name);\n        }\n      });\n\n    } catch (err) {\n      console.error(\"Error setting up Spline interactions:\", err);\n      setError(\"Failed to set up keyboard interactions.\");\n      toast({\n        title: \"Error\",\n        description: \"Failed to load keyboard interactions. Please try refreshing the page.\",\n        variant: \"destructive\"\n      });\n    }\n  };\n\n  const handleKeyPress = (keyId: string) => {\n    try {\n      // Map Spline object names to skill IDs\n      const skillMapping: { [key: string]: string } = {\n        'react_key': 'react',\n        'typescript_key': 'typescript',\n        'nextjs_key': 'nextjs',\n        'nodejs_key': 'nodejs',\n        'python_key': 'python',\n        'javascript_key': 'javascript',\n        'html_key': 'html',\n        'css_key': 'css',\n        'git_key': 'git',\n        'docker_key': 'docker',\n        // Add more mappings as needed based on your Spline model\n      };\n\n      const skillId = skillMapping[keyId];\n      if (skillId) {\n        const skillData = getSkillById(skillId);\n        if (skillData) {\n          setSelectedSkill(skillData);\n          setShowSkillInfo(true);\n        }\n      }\n    } catch (err) {\n      console.error(\"Error selecting key:\", err);\n      setError(\"Failed to select key.\");\n    }\n  };\n\n  // Handle closing the skill card\n  const handleCloseSkillCard = () => {\n    setShowSkillInfo(false);\n    setSelectedSkill(null);\n  };\n\n  // Handle keyboard input for accessibility\n  useEffect(() => {\n    if (typeof window === 'undefined') return;\n\n    const handleKeyboardPress = (e: KeyboardEvent) => {\n      try {\n        // Check for escape key to close skill info\n        if (e.key === 'Escape' && showSkillInfo) {\n          handleCloseSkillCard();\n          return;\n        }\n\n        // Simple key mapping for common skills\n        const keyMapping: { [key: string]: string } = {\n          'r': 'react_key',\n          't': 'typescript_key',\n          'n': 'nextjs_key',\n          'j': 'javascript_key',\n          'p': 'python_key',\n          'h': 'html_key',\n          'c': 'css_key',\n          'g': 'git_key',\n          'd': 'docker_key',\n        };\n\n        const splineKey = keyMapping[e.key.toLowerCase()];\n        if (splineKey) {\n          handleKeyPress(splineKey);\n        }\n      } catch (err) {\n        console.error(\"Error processing key press:\", err);\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyboardPress);\n\n    return () => {\n      window.removeEventListener('keydown', handleKeyboardPress);\n    };\n  }, [showSkillInfo]);\n\n  // Error state display\n  if (error) {\n    return (\n      <div className=\"w-full h-[400px] flex items-center justify-center bg-github-dark/50 rounded-lg\">\n        <div className=\"text-center p-6\">\n          <p className=\"text-red-400 mb-2\">Error: {error}</p>\n          <button\n            onClick={() => setError(null)}\n            className=\"px-4 py-2 bg-github-light/30 text-neon-green rounded-md hover:bg-github-light/50 transition-colors\"\n          >\n            Retry\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        exit={{ opacity: 0 }}\n        transition={{ duration: 0.5 }}\n        className=\"my-4 w-full flex flex-col items-center justify-center\"\n      >\n        {/* Display selected skill info at the top */}\n        {selectedSkill && (\n          <motion.div\n            initial={{ opacity: 0, y: -10 }}\n            animate={{ opacity: 1, y: 0 }}\n            className=\"mb-8 text-center\"\n          >\n            <h3 className=\"text-2xl font-bold text-white mb-2\">{selectedSkill.name}</h3>\n            <p className=\"text-github-text max-w-xl\">{selectedSkill.description}</p>\n          </motion.div>\n        )}\n\n        {/* Spline 3D keyboard */}\n        <div className=\"w-full max-w-4xl mx-auto relative h-[600px]\">\n          {/* Loading overlay */}\n          {loading && <LoadingScreen />}\n\n          {/* Spline component with the new URL */}\n          <Spline\n            scene=\"https://prod.spline.design/bnffRvBtBHvfSiOW/scene.splinecode\"\n            onLoad={onSplineLoad}\n            style={{ width: '100%', height: '100%' }}\n          />\n\n          {/* Skill information card - positioned on top of Spline for better visibility */}\n          <div className=\"absolute inset-0 pointer-events-none\">\n            <div className=\"pointer-events-auto\">\n              <SkillCard\n                skill={selectedSkill}\n                isVisible={showSkillInfo && !!selectedSkill}\n                onClose={handleCloseSkillCard}\n              />\n            </div>\n          </div>\n        </div>\n      </motion.div>\n\n      {/* Hint text */}\n      <motion.div\n        className=\"text-center mt-2 text-white/70 font-medium\"\n        initial={{ opacity: 0, y: 10 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.5 }}\n      >\n        <p>(hint: click on a key to explore skills)</p>\n      </motion.div>\n    </>\n  );\n};\n\nexport default KeyboardSkillsView;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAEA;AAAA;AACA;AACA,iFAAiF;AACjF;AAVA;;;;;;;;AAmBA,MAAM,YAAY,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAkB;IAC9D,IAAI,CAAC,OAAO,OAAO;IAEnB,kCAAkC;IAClC,MAAM,qBAAqB,CAAC;QAC1B,IAAI,QAAQ,GAAG,OAAO;QACtB,IAAI,QAAQ,GAAG,OAAO;QACtB,IAAI,QAAQ,GAAG,OAAO;QACtB,OAAO;IACT;IAEA,8BAA8B;IAC9B,MAAM,sBAAsB,CAAC;QAC3B,IAAI,cAAc,IAAI,OAAO,WAAW,SAAS;QACjD,IAAI,cAAc,IAAI,OAAO,WAAW,QAAQ;QAChD,OAAO,WAAW,OAAO;IAC3B;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;YAAI,OAAO;QAAK;QAC1C,SAAS;YAAE,SAAS;YAAG,GAAG;YAAG,OAAO;QAAE;QACtC,MAAM;YAAE,SAAS;YAAG,GAAG,CAAC;YAAI,OAAO;QAAK;QACxC,YAAY;YACV,MAAM;YACN,WAAW;YACX,SAAS;YACT,UAAU;QACZ;QACA,WAAU;QACV,OAAO;YAAE,gBAAgB;QAAa;;0BAGtC,8OAAC;gBACC,SAAS;gBACT,WAAU;gBACV,cAAW;0BAEX,cAAA,8OAAC;oBAAI,OAAM;oBAA6B,OAAM;oBAAK,QAAO;oBAAK,SAAQ;oBAAY,MAAK;oBAAO,QAAO;oBAAe,aAAY;oBAAI,eAAc;oBAAQ,gBAAe;;sCACxK,8OAAC;4BAAK,IAAG;4BAAK,IAAG;4BAAI,IAAG;4BAAI,IAAG;;;;;;sCAC/B,8OAAC;4BAAK,IAAG;4BAAI,IAAG;4BAAI,IAAG;4BAAK,IAAG;;;;;;;;;;;;;;;;;0BAKnC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAU;wBACV,OAAO;4BAAE,iBAAiB,MAAM,KAAK;wBAAC;kCAErC,MAAM,IAAI;;;;;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsC,MAAM,IAAI;;;;;;0CAC9D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgC,mBAAmB,MAAM,UAAU;;;;;;kDACnF,8OAAC;wCAAK,WAAU;kDAA2B;;;;;;kDAC3C,8OAAC;wCAAK,WAAU;;4CAAoB,MAAM,UAAU;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;0BAM3D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAyB;;;;;;0CACzC,8OAAC;gCAAK,WAAU;;oCAAwB,MAAM,WAAW;oCAAC;;;;;;;;;;;;;kCAE5D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;4BAAE;4BACpB,SAAS;gCAAE,OAAO,GAAG,MAAM,WAAW,CAAC,CAAC,CAAC;4BAAC;4BAC1C,YAAY;gCAAE,UAAU;gCAAG,MAAM;4BAAU;4BAC3C,WAAU;4BACV,OAAO;gCAAE,iBAAiB,oBAAoB,MAAM,WAAW;4BAAE;;;;;;;;;;;;;;;;;0BAMvE,8OAAC;gBAAE,WAAU;0BAAyC,MAAM,WAAW;;;;;;YAGtE,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,MAAM,GAAG,mBACzC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAI,OAAM;gCAA6B,WAAU;gCAAe,SAAQ;gCAAY,MAAK;gCAAO,QAAO;gCAAe,aAAY;gCAAI,eAAc;gCAAQ,gBAAe;0CAC1K,cAAA,8OAAC;oCAAK,GAAE;;;;;;;;;;;4BACJ;;;;;;;kCAGR,8OAAC;wBAAG,WAAU;kCACX,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC5B,8OAAC;gCAAe,WAAU;;kDACxB,8OAAC;wCAAK,WAAU;kDAAoB;;;;;;oCACnC;;+BAFM;;;;;;;;;;;;;;;;YAUhB,MAAM,SAAS,IAAI,MAAM,SAAS,CAAC,MAAM,GAAG,mBAC3C,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAI,OAAM;gCAA6B,WAAU;gCAAe,SAAQ;gCAAY,MAAK;gCAAO,QAAO;gCAAe,aAAY;gCAAI,eAAc;gCAAQ,gBAAe;;kDAC1K,8OAAC;wCAAK,GAAE;;;;;;kDACR,8OAAC;wCAAK,GAAE;;;;;;kDACR,8OAAC;wCAAK,GAAE;;;;;;;;;;;;4BACJ;;;;;;;kCAGR,8OAAC;wBAAI,WAAU;kCACZ,MAAM,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gCAEV,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,YAAY;oCAAE,OAAO,QAAQ;gCAAI;gCACjC,WAAU;gCACV,OAAO;oCACL,iBAAiB,GAAG,MAAM,KAAK,CAAC,EAAE,CAAC;oCACnC,OAAO,MAAM,KAAK;oCAClB,QAAQ,CAAC,UAAU,EAAE,MAAM,KAAK,CAAC,EAAE,CAAC;gCACtC;0CAEC;+BAXI;;;;;;;;;;;;;;;;;;;;;;AAmBrB;AAEA,MAAM,qBAAqB;IACzB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACjE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAsB;IAE7C,mEAAmE;IACnE,MAAM,eAAe,CAAC;QACpB,UAAU,OAAO,GAAG;QACpB,WAAW;QAEX,IAAI;YACF,iDAAiD;YACjD,UAAU,gBAAgB,CAAC,aAAa,CAAC;gBACvC,IAAI,EAAE,MAAM,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE;oBAC7B,eAAe,EAAE,MAAM,CAAC,IAAI;gBAC9B;YACF;QAEF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,yCAAyC;YACvD,SAAS;YACT,CAAA,GAAA,4HAAA,CAAA,QAAK,AAAD,EAAE;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI;YACF,uCAAuC;YACvC,MAAM,eAA0C;gBAC9C,aAAa;gBACb,kBAAkB;gBAClB,cAAc;gBACd,cAAc;gBACd,cAAc;gBACd,kBAAkB;gBAClB,YAAY;gBACZ,WAAW;gBACX,WAAW;gBACX,cAAc;YAEhB;YAEA,MAAM,UAAU,YAAY,CAAC,MAAM;YACnC,IAAI,SAAS;gBACX,MAAM,YAAY,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD,EAAE;gBAC/B,IAAI,WAAW;oBACb,iBAAiB;oBACjB,iBAAiB;gBACnB;YACF;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wBAAwB;YACtC,SAAS;QACX;IACF;IAEA,gCAAgC;IAChC,MAAM,uBAAuB;QAC3B,iBAAiB;QACjB,iBAAiB;IACnB;IAEA,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAmC;;QAEnC,MAAM;IAmCR,GAAG;QAAC;KAAc;IAElB,sBAAsB;IACtB,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;;4BAAoB;4BAAQ;;;;;;;kCACzC,8OAAC;wBACC,SAAS,IAAM,SAAS;wBACxB,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE;;0BACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,MAAM;oBAAE,SAAS;gBAAE;gBACnB,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;;oBAGT,+BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAG,WAAU;0CAAsC,cAAc,IAAI;;;;;;0CACtE,8OAAC;gCAAE,WAAU;0CAA6B,cAAc,WAAW;;;;;;;;;;;;kCAKvE,8OAAC;wBAAI,WAAU;;4BAEZ,yBAAW,8OAAC,qKAAA,CAAA,UAAa;;;;;0CAG1B,8OAAC,0KAAA,CAAA,UAAM;gCACL,OAAM;gCACN,QAAQ;gCACR,OAAO;oCAAE,OAAO;oCAAQ,QAAQ;gCAAO;;;;;;0CAIzC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,OAAO;wCACP,WAAW,iBAAiB,CAAC,CAAC;wCAC9B,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;0BAEzB,cAAA,8OAAC;8BAAE;;;;;;;;;;;;;AAIX;uCAEe", "debugId": null}}, {"offset": {"line": 3402, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/Skills.tsx"], "sourcesContent": ["\n'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\nimport { handleSkillHover } from '../../utils/animation';\nimport { initScrollAnimations } from '../../utils/animation';\nimport { getSkillsData } from '../../utils/dataUtils';\nimport DisplayToggle from './skills/DisplayToggle';\nimport TabSkillsView from './skills/TabSkillsView';\nimport KeyboardSkillsView from './skills/KeyboardSkillsView';\n\nconst Skills = () => {\n  const [hoveredSkill, setHoveredSkill] = useState<string | null>(null);\n  const [displayStyle, setDisplayStyle] = useState<'tabs' | 'keyboard'>('tabs');\n  const skillsRef = useRef<HTMLDivElement>(null);\n  const { categories, topSkills } = getSkillsData();\n\n  // Initialize GSAP animations when component mounts\n  useEffect(() => {\n    initScrollAnimations();\n  }, []);\n\n  // Handle skill hover\n  const onSkillHover = (element: HTMLElement, skill: string, isEntering: boolean) => {\n    setHoveredSkill(isEntering ? skill : null);\n    handleSkillHover(element, isEntering);\n  };\n\n  // Toggle between display styles\n  const toggleDisplayStyle = () => {\n    setDisplayStyle(prev => prev === 'tabs' ? 'keyboard' : 'tabs');\n  };\n\n  return (\n    <section id=\"skills\" className=\"py-20 bg-github-dark\" ref={skillsRef}>\n      <div className=\"section-container relative\">\n        <motion.h2\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n          viewport={{ once: true }}\n          className=\"section-title\"\n        >\n          Skills\n        </motion.h2>\n\n        <DisplayToggle\n          displayStyle={displayStyle}\n          toggleDisplayStyle={toggleDisplayStyle}\n        />\n\n        {displayStyle === 'tabs' ? (\n          <TabSkillsView\n            categories={categories}\n            topSkills={topSkills}\n            hoveredSkill={hoveredSkill}\n            onSkillHover={onSkillHover}\n          />\n        ) : (\n          <KeyboardSkillsView />\n        )}\n      </div>\n    </section>\n  );\n};\n\nexport default Skills;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAEA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWA,MAAM,SAAS;IACb,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;IAE9C,mDAAmD;IACnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD;IACrB,GAAG,EAAE;IAEL,qBAAqB;IACrB,MAAM,eAAe,CAAC,SAAsB,OAAe;QACzD,gBAAgB,aAAa,QAAQ;QACrC,CAAA,GAAA,yHAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS;IAC5B;IAEA,gCAAgC;IAChC,MAAM,qBAAqB;QACzB,gBAAgB,CAAA,OAAQ,SAAS,SAAS,aAAa;IACzD;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAS,WAAU;QAAuB,KAAK;kBACzD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oBACR,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BACX;;;;;;8BAID,8OAAC,yJAAA,CAAA,UAAa;oBACZ,cAAc;oBACd,oBAAoB;;;;;;gBAGrB,iBAAiB,uBAChB,8OAAC,yJAAA,CAAA,UAAa;oBACZ,YAAY;oBACZ,WAAW;oBACX,cAAc;oBACd,cAAc;;;;;yCAGhB,8OAAC,8JAAA,CAAA,UAAkB;;;;;;;;;;;;;;;;AAK7B;uCAEe", "debugId": null}}, {"offset": {"line": 3511, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/projects/ProjectFilters.tsx"], "sourcesContent": ["\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ntype ProjectFiltersProps = {\n  filter: string;\n  setFilter: (filter: string) => void;\n};\n\nconst ProjectFilters = ({ filter, setFilter }: ProjectFiltersProps) => {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 10 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.3 }}\n      viewport={{ once: true }}\n      className=\"flex flex-wrap gap-3 mb-8 justify-center sm:justify-start\"\n    >\n      <button\n        onClick={() => setFilter('all')}\n        className={`px-4 py-2 rounded-md text-sm transition-colors ${\n          filter === 'all'\n            ? 'bg-neon-green text-black font-medium'\n            : 'bg-github-dark text-github-text hover:bg-github-dark/80'\n        }`}\n      >\n        All Projects\n      </button>\n      <button\n        onClick={() => setFilter('web')}\n        className={`px-4 py-2 rounded-md text-sm transition-colors ${\n          filter === 'web'\n            ? 'bg-neon-green text-black font-medium'\n            : 'bg-github-dark text-github-text hover:bg-github-dark/80'\n        }`}\n      >\n        Web\n      </button>\n      <button\n        onClick={() => setFilter('app')}\n        className={`px-4 py-2 rounded-md text-sm transition-colors ${\n          filter === 'app'\n            ? 'bg-neon-green text-black font-medium'\n            : 'bg-github-dark text-github-text hover:bg-github-dark/80'\n        }`}\n      >\n        Apps\n      </button>\n      <button\n        onClick={() => setFilter('design')}\n        className={`px-4 py-2 rounded-md text-sm transition-colors ${\n          filter === 'design'\n            ? 'bg-neon-green text-black font-medium'\n            : 'bg-github-dark text-github-text hover:bg-github-dark/80'\n        }`}\n      >\n        Design\n      </button>\n    </motion.div>\n  );\n};\n\nexport default ProjectFilters;\n"], "names": [], "mappings": ";;;;AAEA;;;AAOA,MAAM,iBAAiB,CAAC,EAAE,MAAM,EAAE,SAAS,EAAuB;IAChE,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;QAAI;QAC5B,UAAU;YAAE,MAAM;QAAK;QACvB,WAAU;;0BAEV,8OAAC;gBACC,SAAS,IAAM,UAAU;gBACzB,WAAW,CAAC,+CAA+C,EACzD,WAAW,QACP,yCACA,2DACJ;0BACH;;;;;;0BAGD,8OAAC;gBACC,SAAS,IAAM,UAAU;gBACzB,WAAW,CAAC,+CAA+C,EACzD,WAAW,QACP,yCACA,2DACJ;0BACH;;;;;;0BAGD,8OAAC;gBACC,SAAS,IAAM,UAAU;gBACzB,WAAW,CAAC,+CAA+C,EACzD,WAAW,QACP,yCACA,2DACJ;0BACH;;;;;;0BAGD,8OAAC;gBACC,SAAS,IAAM,UAAU;gBACzB,WAAW,CAAC,+CAA+C,EACzD,WAAW,WACP,yCACA,2DACJ;0BACH;;;;;;;;;;;;AAKP;uCAEe", "debugId": null}}, {"offset": {"line": 3586, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/effects/Interactive3DCard.tsx"], "sourcesContent": ["\n'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { motion, useMotionValue, useTransform, useSpring } from 'framer-motion';\n\ninterface Interactive3DCardProps {\n  children: React.ReactNode;\n  className?: string;\n  depth?: number;\n  shadowColor?: string;\n}\n\nconst Interactive3DCard = ({\n  children,\n  className = \"\",\n  depth = 20,\n  shadowColor = \"rgba(63, 185, 80, 0.4)\",\n}: Interactive3DCardProps) => {\n  const cardRef = useRef<HTMLDivElement>(null);\n  const [isHovered, setIsHovered] = useState(false);\n\n  // Motion values for rotation\n  const rotateX = useMotionValue(0);\n  const rotateY = useMotionValue(0);\n\n  // Smooth rotations with springs\n  const springConfig = { stiffness: 150, damping: 20 };\n  const smoothRotateX = useSpring(rotateX, springConfig);\n  const smoothRotateY = useSpring(rotateY, springConfig);\n\n  // Transform for shadow and z-translation\n  const shadowBlur = useTransform(\n    [smoothRotateX, smoothRotateY],\n    ([latestX, latestY]) => {\n      // Fix the type issue by ensuring we're working with numbers\n      const x = typeof latestX === 'number' ? latestX : 0;\n      const y = typeof latestY === 'number' ? latestY : 0;\n      return Math.sqrt(x * x + y * y) * 0.5;\n    }\n  );\n\n  const zTranslate = useSpring(0, springConfig);\n\n  // Handle mouse move for 3D effect\n  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {\n    if (!cardRef.current) return;\n\n    const rect = cardRef.current.getBoundingClientRect();\n    const centerX = rect.left + rect.width / 2;\n    const centerY = rect.top + rect.height / 2;\n\n    // Calculate rotation based on mouse position\n    const mouseX = e.clientX - centerX;\n    const mouseY = e.clientY - centerY;\n\n    // Convert to rotation values (-15 to 15 degrees)\n    const rotX = (mouseY / (rect.height / 2)) * -10;\n    const rotY = (mouseX / (rect.width / 2)) * 10;\n\n    rotateX.set(rotX);\n    rotateY.set(rotY);\n  };\n\n  // Handle mouse enter/leave\n  const handleMouseEnter = () => {\n    setIsHovered(true);\n    zTranslate.set(depth);\n  };\n\n  const handleMouseLeave = () => {\n    setIsHovered(false);\n    rotateX.set(0);\n    rotateY.set(0);\n    zTranslate.set(0);\n  };\n\n  // Clean up effect when component unmounts\n  useEffect(() => {\n    return () => {\n      rotateX.set(0);\n      rotateY.set(0);\n      zTranslate.set(0);\n    };\n  }, [rotateX, rotateY, zTranslate]);\n\n  return (\n    <motion.div\n      ref={cardRef}\n      className={`relative ${className}`}\n      onMouseMove={handleMouseMove}\n      onMouseEnter={handleMouseEnter}\n      onMouseLeave={handleMouseLeave}\n      style={{\n        perspective: 1000,\n        transformStyle: \"preserve-3d\",\n      }}\n      whileTap={{ scale: 0.98 }}\n    >\n      <motion.div\n        style={{\n          rotateX: smoothRotateX,\n          rotateY: smoothRotateY,\n          z: zTranslate,\n          boxShadow: useTransform(\n            shadowBlur,\n            (blur) => `0 ${blur * 2}px ${blur * 4}px ${shadowColor}`\n          ),\n          transformStyle: \"preserve-3d\",\n        }}\n        className=\"h-full w-full transition-colors duration-300\"\n      >\n        {children}\n\n        {/* Visual effect for edges */}\n        <motion.div\n          className=\"absolute inset-0 rounded-lg pointer-events-none border border-neon-green\"\n          style={{\n            opacity: useTransform(zTranslate, [0, depth], [0, 0.3]),\n          }}\n        />\n      </motion.div>\n    </motion.div>\n  );\n};\n\nexport default Interactive3DCard;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAYA,MAAM,oBAAoB,CAAC,EACzB,QAAQ,EACR,YAAY,EAAE,EACd,QAAQ,EAAE,EACV,cAAc,wBAAwB,EACf;IACvB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,6BAA6B;IAC7B,MAAM,UAAU,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IAC/B,MAAM,UAAU,CAAA,GAAA,kLAAA,CAAA,iBAAc,AAAD,EAAE;IAE/B,gCAAgC;IAChC,MAAM,eAAe;QAAE,WAAW;QAAK,SAAS;IAAG;IACnD,MAAM,gBAAgB,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,SAAS;IACzC,MAAM,gBAAgB,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,SAAS;IAEzC,yCAAyC;IACzC,MAAM,aAAa,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAC5B;QAAC;QAAe;KAAc,EAC9B,CAAC,CAAC,SAAS,QAAQ;QACjB,4DAA4D;QAC5D,MAAM,IAAI,OAAO,YAAY,WAAW,UAAU;QAClD,MAAM,IAAI,OAAO,YAAY,WAAW,UAAU;QAClD,OAAO,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,KAAK;IACpC;IAGF,MAAM,aAAa,CAAA,GAAA,yKAAA,CAAA,YAAS,AAAD,EAAE,GAAG;IAEhC,kCAAkC;IAClC,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,QAAQ,OAAO,EAAE;QAEtB,MAAM,OAAO,QAAQ,OAAO,CAAC,qBAAqB;QAClD,MAAM,UAAU,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;QACzC,MAAM,UAAU,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;QAEzC,6CAA6C;QAC7C,MAAM,SAAS,EAAE,OAAO,GAAG;QAC3B,MAAM,SAAS,EAAE,OAAO,GAAG;QAE3B,iDAAiD;QACjD,MAAM,OAAO,AAAC,SAAS,CAAC,KAAK,MAAM,GAAG,CAAC,IAAK,CAAC;QAC7C,MAAM,OAAO,AAAC,SAAS,CAAC,KAAK,KAAK,GAAG,CAAC,IAAK;QAE3C,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;IACd;IAEA,2BAA2B;IAC3B,MAAM,mBAAmB;QACvB,aAAa;QACb,WAAW,GAAG,CAAC;IACjB;IAEA,MAAM,mBAAmB;QACvB,aAAa;QACb,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,WAAW,GAAG,CAAC;IACjB;IAEA,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC;YACZ,WAAW,GAAG,CAAC;QACjB;IACF,GAAG;QAAC;QAAS;QAAS;KAAW;IAEjC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAW,CAAC,SAAS,EAAE,WAAW;QAClC,aAAa;QACb,cAAc;QACd,cAAc;QACd,OAAO;YACL,aAAa;YACb,gBAAgB;QAClB;QACA,UAAU;YAAE,OAAO;QAAK;kBAExB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,OAAO;gBACL,SAAS;gBACT,SAAS;gBACT,GAAG;gBACH,WAAW,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EACpB,YACA,CAAC,OAAS,CAAC,EAAE,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,aAAa;gBAE1D,gBAAgB;YAClB;YACA,WAAU;;gBAET;8BAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,OAAO;wBACL,SAAS,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,YAAY;4BAAC;4BAAG;yBAAM,EAAE;4BAAC;4BAAG;yBAAI;oBACxD;;;;;;;;;;;;;;;;;AAKV;uCAEe", "debugId": null}}, {"offset": {"line": 3720, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/ProjectCard.tsx"], "sourcesContent": ["\n'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { projectCardHover } from '../../utils/animation';\nimport Interactive3DCard from '../effects/Interactive3DCard';\n\ninterface ProjectCardProps {\n  project: {\n    title: string;\n    description: string;\n    tags: string[];\n    category: string;\n    imageUrl: string;\n  };\n  delay?: number;\n}\n\nconst ProjectCard = ({ project, delay = 0 }: ProjectCardProps) => {\n  const [isHovered, setIsHovered] = useState(false);\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5, delay }}\n      viewport={{ once: true }}\n    >\n      <Interactive3DCard className=\"h-full\">\n        <div\n          className=\"bg-github-dark border border-github-border rounded-lg overflow-hidden h-full transition-all duration-300\"\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n        >\n          <div className=\"aspect-video w-full overflow-hidden\">\n            <motion.img\n              src={project.imageUrl}\n              alt={project.title}\n              className=\"w-full h-full object-cover object-center\"\n              animate={{\n                scale: isHovered ? 1.05 : 1,\n              }}\n              transition={{ duration: 0.4 }}\n            />\n          </div>\n\n          <div className=\"p-6\">\n            <div className=\"flex justify-between items-start\">\n              <motion.h3\n                className=\"text-xl font-bold text-white\"\n                animate={{ x: isHovered ? 3 : 0 }}\n                transition={{ duration: 0.2 }}\n              >\n                {project.title}\n              </motion.h3>\n\n              <div className=\"flex flex-wrap gap-2\">\n                {project.tags.map((tag, tagIndex) => (\n                  <span\n                    key={tagIndex}\n                    className={`tech-badge px-2 py-1 text-xs rounded ${\n                      tag === \"HTML\"\n                        ? \"bg-tech-html/20 text-tech-html border border-tech-html/30\"\n                        : tag === \"CSS\"\n                        ? \"bg-tech-css/20 text-tech-css border border-tech-css/30\"\n                        : tag === \"TypeScript\"\n                        ? \"bg-tech-ts/20 text-tech-ts border border-tech-ts/30\"\n                        : tag === \"JavaScript\"\n                        ? \"bg-yellow-500/20 text-yellow-500 border border-yellow-500/30\"\n                        : tag === \"React\"\n                        ? \"bg-blue-400/20 text-blue-400 border border-blue-400/30\"\n                        : tag === \"Next.js\"\n                        ? \"bg-black/40 text-white border border-white/30\"\n                        : tag === \"Tailwind\"\n                        ? \"bg-teal-500/20 text-teal-500 border border-teal-500/30\"\n                        : tag === \"Design\"\n                        ? \"bg-purple-500/20 text-purple-500 border border-purple-500/30\"\n                        : tag === \"Cloud\"\n                        ? \"bg-blue-600/20 text-blue-600 border border-blue-600/30\"\n                        : \"bg-gray-500/20 text-gray-500 border border-gray-500/30\"\n                      }`}\n                  >\n                    {tag}\n                  </span>\n                ))}\n              </div>\n            </div>\n\n            <p className=\"mt-4 text-github-text\">{project.description}</p>\n\n            <motion.div\n              className=\"mt-6 flex gap-3\"\n              animate={{ y: isHovered ? 0 : 10, opacity: isHovered ? 1 : 0.8 }}\n              transition={{ duration: 0.3 }}\n            >\n              <a\n                href=\"#\"\n                className=\"px-4 py-2 bg-neon-green/20 text-neon-green rounded-md hover:bg-neon-green/30 transition-colors\"\n              >\n                Demo\n              </a>\n              <a\n                href=\"#\"\n                className=\"px-4 py-2 bg-github-light text-github-text rounded-md hover:bg-github-light/80 transition-colors\"\n              >\n                Source\n              </a>\n            </motion.div>\n          </div>\n        </div>\n      </Interactive3DCard>\n    </motion.div>\n  );\n};\n\nexport default ProjectCard;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AALA;;;;;AAkBA,MAAM,cAAc,CAAC,EAAE,OAAO,EAAE,QAAQ,CAAC,EAAoB;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK;QAAM;QACnC,UAAU;YAAE,MAAM;QAAK;kBAEvB,cAAA,8OAAC,kJAAA,CAAA,UAAiB;YAAC,WAAU;sBAC3B,cAAA,8OAAC;gBACC,WAAU;gBACV,cAAc,IAAM,aAAa;gBACjC,cAAc,IAAM,aAAa;;kCAEjC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,KAAK,QAAQ,QAAQ;4BACrB,KAAK,QAAQ,KAAK;4BAClB,WAAU;4BACV,SAAS;gCACP,OAAO,YAAY,OAAO;4BAC5B;4BACA,YAAY;gCAAE,UAAU;4BAAI;;;;;;;;;;;kCAIhC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wCACR,WAAU;wCACV,SAAS;4CAAE,GAAG,YAAY,IAAI;wCAAE;wCAChC,YAAY;4CAAE,UAAU;wCAAI;kDAE3B,QAAQ,KAAK;;;;;;kDAGhB,8OAAC;wCAAI,WAAU;kDACZ,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,yBACtB,8OAAC;gDAEC,WAAW,CAAC,qCAAqC,EAC/C,QAAQ,SACJ,8DACA,QAAQ,QACR,2DACA,QAAQ,eACR,wDACA,QAAQ,eACR,iEACA,QAAQ,UACR,2DACA,QAAQ,YACR,kDACA,QAAQ,aACR,2DACA,QAAQ,WACR,iEACA,QAAQ,UACR,2DACA,0DACF;0DAEH;+CAvBI;;;;;;;;;;;;;;;;0CA6Bb,8OAAC;gCAAE,WAAU;0CAAyB,QAAQ,WAAW;;;;;;0CAEzD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,GAAG,YAAY,IAAI;oCAAI,SAAS,YAAY,IAAI;gCAAI;gCAC/D,YAAY;oCAAE,UAAU;gCAAI;;kDAE5B,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 3892, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/projects/ProjectGrid.tsx"], "sourcesContent": ["\n'use client';\n\nimport { motion } from 'framer-motion';\nimport ProjectCard from '../ProjectCard';\n\ntype ProjectGridProps = {\n  projects: {\n    title: string;\n    description: string;\n    tags: string[];\n    category: string;\n    imageUrl: string;\n  }[];\n  filter: string;\n};\n\nconst ProjectGrid = ({ projects, filter }: ProjectGridProps) => {\n  // Filter projects based on selected category\n  const filteredProjects = filter === 'all'\n    ? projects\n    : projects.filter(project => project.category === filter);\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n      {filteredProjects.map((project, index) => (\n        <ProjectCard\n          key={project.title}\n          project={project}\n          delay={index * 0.1}\n        />\n      ))}\n    </div>\n  );\n};\n\nexport default ProjectGrid;\n"], "names": [], "mappings": ";;;;AAIA;AAHA;;;AAgBA,MAAM,cAAc,CAAC,EAAE,QAAQ,EAAE,MAAM,EAAoB;IACzD,6CAA6C;IAC7C,MAAM,mBAAmB,WAAW,QAChC,WACA,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAEpD,qBACE,8OAAC;QAAI,WAAU;kBACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC,6IAAA,CAAA,UAAW;gBAEV,SAAS;gBACT,OAAO,QAAQ;eAFV,QAAQ,KAAK;;;;;;;;;;AAO5B;uCAEe", "debugId": null}}, {"offset": {"line": 3926, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/projects/ProjectCTA.tsx"], "sourcesContent": ["\nimport { motion } from 'framer-motion';\n\nconst ProjectCTA = () => {\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5, delay: 0.5 }}\n      viewport={{ once: true }}\n      className=\"mt-10 text-center\"\n    >\n      <a\n        href=\"#contact\"\n        className=\"inline-flex items-center px-6 py-3 bg-neon-green text-black font-medium rounded-md hover:bg-neon-green/90 transition-colors\"\n      >\n        <span>Interested in working together?</span>\n        <svg\n          xmlns=\"http://www.w3.org/2000/svg\"\n          className=\"h-5 w-5 ml-2\"\n          viewBox=\"0 0 24 24\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          strokeWidth=\"2\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        >\n          <path d=\"M5 12h14\" />\n          <path d=\"m12 5 7 7-7 7\" />\n        </svg>\n      </a>\n    </motion.div>\n  );\n};\n\nexport default ProjectCTA;\n"], "names": [], "mappings": ";;;;AACA;;;AAEA,MAAM,aAAa;IACjB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK,OAAO;QAAI;QACxC,UAAU;YAAE,MAAM;QAAK;QACvB,WAAU;kBAEV,cAAA,8OAAC;YACC,MAAK;YACL,WAAU;;8BAEV,8OAAC;8BAAK;;;;;;8BACN,8OAAC;oBACC,OAAM;oBACN,WAAU;oBACV,SAAQ;oBACR,MAAK;oBACL,QAAO;oBACP,aAAY;oBACZ,eAAc;oBACd,gBAAe;;sCAEf,8OAAC;4BAAK,GAAE;;;;;;sCACR,8OAAC;4BAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;AAKlB;uCAEe", "debugId": null}}, {"offset": {"line": 4011, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/Projects.tsx"], "sourcesContent": ["\n'use client';\n\nimport { motion } from 'framer-motion';\nimport { useState } from 'react';\nimport ProjectFilters from './projects/ProjectFilters';\nimport ProjectGrid from './projects/ProjectGrid';\nimport ProjectCTA from './projects/ProjectCTA';\nimport { getProjectsData } from '../../utils/dataUtils';\n\nconst Projects = () => {\n  const [filter, setFilter] = useState('all');\n  const projects = getProjectsData();\n\n  return (\n    <section id=\"projects\" className=\"py-20 bg-github-light\">\n      <div className=\"section-container\">\n        <motion.h2\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n          viewport={{ once: true }}\n          className=\"section-title\"\n        >\n          Projects\n        </motion.h2>\n\n        <ProjectFilters filter={filter} setFilter={setFilter} />\n        <ProjectGrid projects={projects} filter={filter} />\n        <ProjectCTA />\n      </div>\n    </section>\n  );\n};\n\nexport default Projects;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,WAAW;IACf,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD;IAE/B,qBACE,8OAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oBACR,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BACX;;;;;;8BAID,8OAAC,4JAAA,CAAA,UAAc;oBAAC,QAAQ;oBAAQ,WAAW;;;;;;8BAC3C,8OAAC,yJAAA,CAAA,UAAW;oBAAC,UAAU;oBAAU,QAAQ;;;;;;8BACzC,8OAAC,wJAAA,CAAA,UAAU;;;;;;;;;;;;;;;;AAInB;uCAEe", "debugId": null}}, {"offset": {"line": 4100, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/ui/collapsible.tsx"], "sourcesContent": ["import * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\"\n\nconst Collapsible = CollapsiblePrimitive.Root\n\nconst CollapsibleTrigger = CollapsiblePrimitive.CollapsibleTrigger\n\nconst CollapsibleContent = CollapsiblePrimitive.CollapsibleContent\n\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent }\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,cAAc,uKAAA,CAAA,OAAyB;AAE7C,MAAM,qBAAqB,uKAAA,CAAA,qBAAuC;AAElE,MAAM,qBAAqB,uKAAA,CAAA,qBAAuC", "debugId": null}}, {"offset": {"line": 4117, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/Experience.tsx"], "sourcesContent": ["\n'use client';\n\nimport { motion } from 'framer-motion';\nimport { useState } from 'react';\nimport { Collapsible, CollapsibleContent, CollapsibleTrigger } from \"../ui/collapsible\";\n\ninterface ExperienceItem {\n  id: string;\n  title: string;\n  company: string;\n  period: string;\n  location: string;\n  description?: string;\n  skills?: string[];\n  type: string;\n}\n\nconst Experience = () => {\n  const [activeTab, setActiveTab] = useState('work');\n  const [expandedItem, setExpandedItem] = useState<string | null>(null);\n\n  const toggleItem = (id: string) => {\n    setExpandedItem(expandedItem === id ? null : id);\n  };\n\n  const experiences: ExperienceItem[] = [\n    {\n      id: \"exp1\",\n      title: \"Field Associate\",\n      company: \"Sachetparyant\",\n      period: \"Sep 2021 - Jul 2023\",\n      location: \"India\",\n      type: \"Part-time\",\n      description: \"Database Head & Field Associate at Sachetparyant's Project Shikshan. Responsible for managing multiple database systems and ensuring data integrity across projects.\",\n      skills: [\"Database Management\", \"Team Leadership\", \"Data Analysis\"]\n    },\n    {\n      id: \"exp2\",\n      title: \"Field Associate\",\n      company: \"Sachetparyant\",\n      period: \"Sep 2021 - Apr 2022\",\n      location: \"India\",\n      type: \"Part-time\",\n      description: \"Database Head & Field Associate at Sachetparyant's Project Shikshan. Worked with stakeholders to identify data needs and implement appropriate solutions.\",\n      skills: [\"Team Management\", \"Data Analysis\", \"Stakeholder Communication\"]\n    },\n    {\n      id: \"exp3\",\n      title: \"Field Associate\",\n      company: \"Sachetparyant\",\n      period: \"Sep 2021 - Sep 2021\",\n      location: \"India\",\n      type: \"Internship\",\n      description: \"Assisted in field operations and data collection activities. Participated in team meetings and contributed to project documentation.\",\n      skills: [\"Field Operations\", \"Data Collection\", \"Documentation\"]\n    },\n    {\n      id: \"exp4\",\n      title: \"Executive\",\n      company: \"Sachetparyant\",\n      period: \"Jul 2021 - Sep 2021\",\n      location: \"India\",\n      type: \"Internship\",\n      description: \"Supported executive team in daily operations and special projects. Developed reporting templates and assisted with presentation materials.\",\n      skills: [\"Executive Support\", \"Reporting\", \"Office Administration\"]\n    }\n  ];\n\n  const education = [\n    {\n      id: \"edu1\",\n      school: \"Newton School of Technology\",\n      degree: \"Bachelor of Technology - BTech, Computer Science\",\n      period: \"Aug 2024 - Jul 2028\",\n      description: \"Focusing on advanced programming concepts, data structures, algorithms, and software engineering principles. Participating in coding competitions and tech community events.\",\n      skills: [\"Programming\", \"Python\", \"Data Structures\", \"Algorithms\"]\n    },\n    {\n      id: \"edu2\",\n      school: \"Royal Senior Secondary School\",\n      degree: \"Senior Secondary School, PCM\",\n      period: \"Apr 2022 - May 2024\",\n      description: \"Completed secondary education with focus on Physics, Chemistry, and Mathematics (PCM). Participated in science exhibitions and mathematical olympiads.\",\n      skills: [\"Mathematics\", \"Physics\", \"Chemistry\", \"Problem Solving\"]\n    }\n  ];\n\n  const certifications = [\n    {\n      id: \"cert1\",\n      name: \"AI For Everyone\",\n      issuer: \"DeepLearning.AI\",\n      date: \"Mar 2025\",\n      certId: \"GQIFS41IFAYR\"\n    },\n    {\n      id: \"cert2\",\n      name: \"Generative AI for Everyone\",\n      issuer: \"DeepLearning.AI\",\n      date: \"Mar 2025\",\n      certId: \"R2CGBN98KY1W\"\n    }\n  ];\n\n  const achievements = [\n    {\n      id: \"ach1\",\n      title: \"Postman API Fundamentals Student Expert\",\n      date: \"Feb 2025\",\n      description: \"Mastered API development and testing with Postman.\"\n    },\n    {\n      id: \"ach2\",\n      title: \"Certified Machine Learning Specialist\",\n      date: \"Jan 2025\",\n      description: \"Completed a comprehensive course on machine learning.\"\n    },\n    {\n      id: \"ach3\",\n      title: \"1st Place - Robo Soccer Competition\",\n      date: \"Dec 2024\",\n      description: \"Led a team to victory at the national Robo Soccer competition.\"\n    },\n    {\n      id: \"ach4\",\n      title: \"Tekron 2025 Organizing Committee Member\",\n      date: \"Nov 2024\",\n      description: \"Contributed to organizing one of the largest technical events at my university.\"\n    }\n  ];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { opacity: 1, y: 0 }\n  };\n\n  return (\n    <section id=\"experience\" className=\"py-20 bg-github-dark\">\n      <div className=\"section-container\">\n        <motion.h2\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n          viewport={{ once: true }}\n          className=\"section-title\"\n        >\n          Experience\n        </motion.h2>\n\n        <div className=\"flex justify-center mb-12\">\n          <div className=\"inline-flex rounded-md shadow-sm p-1 bg-github-light\">\n            <button\n              onClick={() => setActiveTab('work')}\n              className={`px-4 py-2 text-sm font-medium rounded-md ${\n                activeTab === 'work'\n                  ? 'bg-neon-green text-black'\n                  : 'text-github-text hover:text-white'\n              }`}\n            >\n              Work Experience\n            </button>\n            <button\n              onClick={() => setActiveTab('education')}\n              className={`px-4 py-2 text-sm font-medium rounded-md ${\n                activeTab === 'education'\n                  ? 'bg-neon-purple text-black'\n                  : 'text-github-text hover:text-white'\n              }`}\n            >\n              Education\n            </button>\n            <button\n              onClick={() => setActiveTab('certifications')}\n              className={`px-4 py-2 text-sm font-medium rounded-md ${\n                activeTab === 'certifications'\n                  ? 'bg-neon-blue text-black'\n                  : 'text-github-text hover:text-white'\n              }`}\n            >\n              Certifications\n            </button>\n            <button\n              onClick={() => setActiveTab('achievements')}\n              className={`px-4 py-2 text-sm font-medium rounded-md ${\n                activeTab === 'achievements'\n                  ? 'bg-neon-green text-black'\n                  : 'text-github-text hover:text-white'\n              }`}\n            >\n              Achievements\n            </button>\n          </div>\n        </div>\n\n        <div>\n          {/* Work Experience Tab */}\n          {activeTab === 'work' && (\n            <motion.div\n              variants={containerVariants}\n              initial=\"hidden\"\n              animate=\"visible\"\n              className=\"space-y-4\"\n            >\n              <motion.h3\n                variants={itemVariants}\n                className=\"text-xl font-bold text-white flex items-center\"\n              >\n                <span className=\"inline-block w-3 h-3 bg-neon-green rounded-full mr-2\"></span>\n                Work Experience\n              </motion.h3>\n\n              {experiences.map((exp) => (\n                <motion.div\n                  key={exp.id}\n                  variants={itemVariants}\n                  className=\"bg-github-light rounded-lg border border-github-border overflow-hidden transition-all duration-300\"\n                >\n                  <Collapsible\n                    open={expandedItem === exp.id}\n                    onOpenChange={() => toggleItem(exp.id)}\n                  >\n                    <div className=\"p-6\">\n                      <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-center\">\n                        <div>\n                          <h4 className=\"font-semibold text-white text-lg\">{exp.title}</h4>\n                          <p className=\"text-neon-green\">{exp.company}</p>\n                        </div>\n                        <div className=\"mt-2 sm:mt-0 text-right\">\n                          <p className=\"text-github-text text-sm\">{exp.type}</p>\n                          <p className=\"mt-1 text-github-text text-sm\">{exp.period}</p>\n                        </div>\n                      </div>\n\n                      <CollapsibleTrigger className=\"mt-4 text-sm text-github-text hover:text-white transition-colors flex items-center\">\n                        {expandedItem === exp.id ? 'Show less' : 'Show more'}\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          width=\"16\"\n                          height=\"16\"\n                          viewBox=\"0 0 24 24\"\n                          fill=\"none\"\n                          stroke=\"currentColor\"\n                          strokeWidth=\"2\"\n                          strokeLinecap=\"round\"\n                          strokeLinejoin=\"round\"\n                          className={`ml-2 transition-transform ${expandedItem === exp.id ? 'rotate-180' : ''}`}\n                        >\n                          <path d=\"m6 9 6 6 6-6\"/>\n                        </svg>\n                      </CollapsibleTrigger>\n                    </div>\n\n                    <CollapsibleContent>\n                      <div className=\"px-6 pb-6 border-t border-github-border pt-4 mt-4\">\n                        {exp.description && (\n                          <p className=\"text-github-text\">{exp.description}</p>\n                        )}\n                        {exp.skills && (\n                          <div className=\"mt-3 flex flex-wrap gap-2\">\n                            {exp.skills.map((skill, skillIndex) => (\n                              <span\n                                key={skillIndex}\n                                className=\"tech-badge bg-github-dark\"\n                              >\n                                {skill}\n                              </span>\n                            ))}\n                          </div>\n                        )}\n                      </div>\n                    </CollapsibleContent>\n                  </Collapsible>\n                </motion.div>\n              ))}\n            </motion.div>\n          )}\n\n          {/* Education Tab */}\n          {activeTab === 'education' && (\n            <motion.div\n              variants={containerVariants}\n              initial=\"hidden\"\n              animate=\"visible\"\n              className=\"space-y-4\"\n            >\n              <motion.h3\n                variants={itemVariants}\n                className=\"text-xl font-bold text-white flex items-center\"\n              >\n                <span className=\"inline-block w-3 h-3 bg-neon-purple rounded-full mr-2\"></span>\n                Education\n              </motion.h3>\n\n              {education.map((edu) => (\n                <motion.div\n                  key={edu.id}\n                  variants={itemVariants}\n                  className=\"bg-github-light rounded-lg border border-github-border overflow-hidden\"\n                >\n                  <Collapsible\n                    open={expandedItem === edu.id}\n                    onOpenChange={() => toggleItem(edu.id)}\n                  >\n                    <div className=\"p-6\">\n                      <h4 className=\"font-semibold text-white text-lg\">{edu.school}</h4>\n                      <p className=\"text-neon-purple\">{edu.degree}</p>\n                      <p className=\"mt-2 text-github-text text-sm\">{edu.period}</p>\n\n                      <CollapsibleTrigger className=\"mt-4 text-sm text-github-text hover:text-white transition-colors flex items-center\">\n                        {expandedItem === edu.id ? 'Show less' : 'Show more'}\n                        <svg\n                          xmlns=\"http://www.w3.org/2000/svg\"\n                          width=\"16\"\n                          height=\"16\"\n                          viewBox=\"0 0 24 24\"\n                          fill=\"none\"\n                          stroke=\"currentColor\"\n                          strokeWidth=\"2\"\n                          strokeLinecap=\"round\"\n                          strokeLinejoin=\"round\"\n                          className={`ml-2 transition-transform ${expandedItem === edu.id ? 'rotate-180' : ''}`}\n                        >\n                          <path d=\"m6 9 6 6 6-6\"/>\n                        </svg>\n                      </CollapsibleTrigger>\n                    </div>\n\n                    <CollapsibleContent>\n                      <div className=\"px-6 pb-6 border-t border-github-border pt-4 mt-4\">\n                        {edu.description && (\n                          <p className=\"text-github-text\">{edu.description}</p>\n                        )}\n                        {edu.skills && (\n                          <div className=\"mt-3 flex flex-wrap gap-2\">\n                            {edu.skills.map((skill, skillIndex) => (\n                              <span\n                                key={skillIndex}\n                                className=\"tech-badge bg-github-dark\"\n                              >\n                                {skill}\n                              </span>\n                            ))}\n                          </div>\n                        )}\n                      </div>\n                    </CollapsibleContent>\n                  </Collapsible>\n                </motion.div>\n              ))}\n            </motion.div>\n          )}\n\n          {/* Certifications Tab */}\n          {activeTab === 'certifications' && (\n            <motion.div\n              variants={containerVariants}\n              initial=\"hidden\"\n              animate=\"visible\"\n              className=\"space-y-4\"\n            >\n              <motion.h3\n                variants={itemVariants}\n                className=\"text-xl font-bold text-white flex items-center\"\n              >\n                <span className=\"inline-block w-3 h-3 bg-neon-blue rounded-full mr-2\"></span>\n                Certifications\n              </motion.h3>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                {certifications.map((cert) => (\n                  <motion.div\n                    key={cert.id}\n                    variants={itemVariants}\n                    whileHover={{ y: -5, boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)' }}\n                    className=\"bg-github-light rounded-lg p-6 border border-github-border\"\n                  >\n                    <h4 className=\"font-semibold text-white text-lg\">{cert.name}</h4>\n                    <p className=\"text-neon-blue\">{cert.issuer}</p>\n                    <div className=\"mt-2 text-github-text text-sm flex justify-between\">\n                      <span>Issued {cert.date}</span>\n                      <span className=\"text-xs bg-github-dark px-2 py-1 rounded-full\">ID: {cert.certId}</span>\n                    </div>\n                    <div className=\"mt-4 flex\">\n                      <button className=\"text-sm text-white px-3 py-1 border border-neon-blue/50 rounded hover:bg-neon-blue/10 transition-colors\">\n                        Show credential\n                      </button>\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n          )}\n\n          {/* Achievements Tab */}\n          {activeTab === 'achievements' && (\n            <motion.div\n              variants={containerVariants}\n              initial=\"hidden\"\n              animate=\"visible\"\n              className=\"space-y-4\"\n            >\n              <motion.h3\n                variants={itemVariants}\n                className=\"text-xl font-bold text-white flex items-center\"\n              >\n                <span className=\"inline-block w-3 h-3 bg-neon-green rounded-full mr-2\"></span>\n                Achievements\n              </motion.h3>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                {achievements.map((achievement) => (\n                  <motion.div\n                    key={achievement.id}\n                    variants={itemVariants}\n                    whileHover={{ y: -5, boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1)' }}\n                    className=\"bg-github-light rounded-lg p-6 border border-github-border\"\n                  >\n                    <div className=\"flex justify-between items-start\">\n                      <h4 className=\"font-semibold text-white text-lg\">{achievement.title}</h4>\n                      <span className=\"bg-github-dark text-neon-green px-2 py-1 text-xs rounded-full\">\n                        {achievement.date}\n                      </span>\n                    </div>\n                    <p className=\"mt-2 text-github-text\">{achievement.description}</p>\n                  </motion.div>\n                ))}\n              </div>\n            </motion.div>\n          )}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Experience;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAJA;;;;;AAiBA,MAAM,aAAa;IACjB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,MAAM,aAAa,CAAC;QAClB,gBAAgB,iBAAiB,KAAK,OAAO;IAC/C;IAEA,MAAM,cAAgC;QACpC;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,UAAU;YACV,MAAM;YACN,aAAa;YACb,QAAQ;gBAAC;gBAAuB;gBAAmB;aAAgB;QACrE;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,UAAU;YACV,MAAM;YACN,aAAa;YACb,QAAQ;gBAAC;gBAAmB;gBAAiB;aAA4B;QAC3E;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,UAAU;YACV,MAAM;YACN,aAAa;YACb,QAAQ;gBAAC;gBAAoB;gBAAmB;aAAgB;QAClE;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,QAAQ;YACR,UAAU;YACV,MAAM;YACN,aAAa;YACb,QAAQ;gBAAC;gBAAqB;gBAAa;aAAwB;QACrE;KACD;IAED,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,aAAa;YACb,QAAQ;gBAAC;gBAAe;gBAAU;gBAAmB;aAAa;QACpE;QACA;YACE,IAAI;YACJ,QAAQ;YACR,QAAQ;YACR,QAAQ;YACR,aAAa;YACb,QAAQ;gBAAC;gBAAe;gBAAW;gBAAa;aAAkB;QACpE;KACD;IAED,MAAM,iBAAiB;QACrB;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,MAAM;YACN,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,MAAM;YACN,QAAQ;QACV;KACD;IAED,MAAM,eAAe;QACnB;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,MAAM;YACN,aAAa;QACf;KACD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAa,WAAU;kBACjC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oBACR,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BACX;;;;;;8BAID,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,SACV,6BACA,qCACJ;0CACH;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,cACV,8BACA,qCACJ;0CACH;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,mBACV,4BACA,qCACJ;0CACH;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,iBACV,6BACA,qCACJ;0CACH;;;;;;;;;;;;;;;;;8BAML,8OAAC;;wBAEE,cAAc,wBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,SAAQ;4BACR,SAAQ;4BACR,WAAU;;8CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,UAAU;oCACV,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;;;;;;wCAA8D;;;;;;;gCAI/E,YAAY,GAAG,CAAC,CAAC,oBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,UAAU;wCACV,WAAU;kDAEV,cAAA,8OAAC,uIAAA,CAAA,cAAW;4CACV,MAAM,iBAAiB,IAAI,EAAE;4CAC7B,cAAc,IAAM,WAAW,IAAI,EAAE;;8DAErC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;sFACC,8OAAC;4EAAG,WAAU;sFAAoC,IAAI,KAAK;;;;;;sFAC3D,8OAAC;4EAAE,WAAU;sFAAmB,IAAI,OAAO;;;;;;;;;;;;8EAE7C,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;sFAA4B,IAAI,IAAI;;;;;;sFACjD,8OAAC;4EAAE,WAAU;sFAAiC,IAAI,MAAM;;;;;;;;;;;;;;;;;;sEAI5D,8OAAC,uIAAA,CAAA,qBAAkB;4DAAC,WAAU;;gEAC3B,iBAAiB,IAAI,EAAE,GAAG,cAAc;8EACzC,8OAAC;oEACC,OAAM;oEACN,OAAM;oEACN,QAAO;oEACP,SAAQ;oEACR,MAAK;oEACL,QAAO;oEACP,aAAY;oEACZ,eAAc;oEACd,gBAAe;oEACf,WAAW,CAAC,0BAA0B,EAAE,iBAAiB,IAAI,EAAE,GAAG,eAAe,IAAI;8EAErF,cAAA,8OAAC;wEAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;8DAKd,8OAAC,uIAAA,CAAA,qBAAkB;8DACjB,cAAA,8OAAC;wDAAI,WAAU;;4DACZ,IAAI,WAAW,kBACd,8OAAC;gEAAE,WAAU;0EAAoB,IAAI,WAAW;;;;;;4DAEjD,IAAI,MAAM,kBACT,8OAAC;gEAAI,WAAU;0EACZ,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,2BACtB,8OAAC;wEAEC,WAAU;kFAET;uEAHI;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAhDd,IAAI,EAAE;;;;;;;;;;;wBAiElB,cAAc,6BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,SAAQ;4BACR,SAAQ;4BACR,WAAU;;8CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,UAAU;oCACV,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;;;;;;wCAA+D;;;;;;;gCAIhF,UAAU,GAAG,CAAC,CAAC,oBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,UAAU;wCACV,WAAU;kDAEV,cAAA,8OAAC,uIAAA,CAAA,cAAW;4CACV,MAAM,iBAAiB,IAAI,EAAE;4CAC7B,cAAc,IAAM,WAAW,IAAI,EAAE;;8DAErC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAoC,IAAI,MAAM;;;;;;sEAC5D,8OAAC;4DAAE,WAAU;sEAAoB,IAAI,MAAM;;;;;;sEAC3C,8OAAC;4DAAE,WAAU;sEAAiC,IAAI,MAAM;;;;;;sEAExD,8OAAC,uIAAA,CAAA,qBAAkB;4DAAC,WAAU;;gEAC3B,iBAAiB,IAAI,EAAE,GAAG,cAAc;8EACzC,8OAAC;oEACC,OAAM;oEACN,OAAM;oEACN,QAAO;oEACP,SAAQ;oEACR,MAAK;oEACL,QAAO;oEACP,aAAY;oEACZ,eAAc;oEACd,gBAAe;oEACf,WAAW,CAAC,0BAA0B,EAAE,iBAAiB,IAAI,EAAE,GAAG,eAAe,IAAI;8EAErF,cAAA,8OAAC;wEAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;8DAKd,8OAAC,uIAAA,CAAA,qBAAkB;8DACjB,cAAA,8OAAC;wDAAI,WAAU;;4DACZ,IAAI,WAAW,kBACd,8OAAC;gEAAE,WAAU;0EAAoB,IAAI,WAAW;;;;;;4DAEjD,IAAI,MAAM,kBACT,8OAAC;gEAAI,WAAU;0EACZ,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,2BACtB,8OAAC;wEAEC,WAAU;kFAET;uEAHI;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAzCd,IAAI,EAAE;;;;;;;;;;;wBA0DlB,cAAc,kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,SAAQ;4BACR,SAAQ;4BACR,WAAU;;8CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,UAAU;oCACV,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;;;;;;wCAA6D;;;;;;;8CAI/E,8OAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,qBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,UAAU;4CACV,YAAY;gDAAE,GAAG,CAAC;gDAAG,WAAW;4CAAsC;4CACtE,WAAU;;8DAEV,8OAAC;oDAAG,WAAU;8DAAoC,KAAK,IAAI;;;;;;8DAC3D,8OAAC;oDAAE,WAAU;8DAAkB,KAAK,MAAM;;;;;;8DAC1C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;gEAAK;gEAAQ,KAAK,IAAI;;;;;;;sEACvB,8OAAC;4DAAK,WAAU;;gEAAgD;gEAAK,KAAK,MAAM;;;;;;;;;;;;;8DAElF,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAO,WAAU;kEAA0G;;;;;;;;;;;;2CAZzH,KAAK,EAAE;;;;;;;;;;;;;;;;wBAuBrB,cAAc,gCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,UAAU;4BACV,SAAQ;4BACR,SAAQ;4BACR,WAAU;;8CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,UAAU;oCACV,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;;;;;;wCAA8D;;;;;;;8CAIhF,8OAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,4BACjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAET,UAAU;4CACV,YAAY;gDAAE,GAAG,CAAC;gDAAG,WAAW;4CAAsC;4CACtE,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAoC,YAAY,KAAK;;;;;;sEACnE,8OAAC;4DAAK,WAAU;sEACb,YAAY,IAAI;;;;;;;;;;;;8DAGrB,8OAAC;oDAAE,WAAU;8DAAyB,YAAY,WAAW;;;;;;;2CAXxD,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBvC;uCAEe", "debugId": null}}, {"offset": {"line": 4925, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/Stats.tsx"], "sourcesContent": ["\n'use client';\n\nimport { motion } from 'framer-motion';\nimport { useState, useEffect, useRef } from 'react';\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Responsive<PERSON>ontaine<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'recharts';\n\nimport { animateGithubGraph } from '../../utils/animation';\n\nconst Stats = () => {\n  const [activeTab, setActiveTab] = useState('contributions');\n  const graphRef = useRef<HTMLDivElement>(null);\n\n  // Animate GitHub contributions graph when it comes into view\n  useEffect(() => {\n    if (graphRef.current) {\n      animateGithubGraph();\n    }\n  }, [graphRef.current]);\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { opacity: 1, y: 0 }\n  };\n\n  const githubStats = {\n    stars: 47,\n    commits: 430,\n    prs: 28,\n    issues: 15,\n    contributions: 12,\n  };\n\n  const contributionData = [\n    { month: 'May', contributions: 42 },\n    { month: 'Jun', contributions: 38 },\n    { month: 'Jul', contributions: 56 },\n    { month: 'Aug', contributions: 72 },\n    { month: 'Sep', contributions: 48 },\n    { month: 'Oct', contributions: 62 },\n    { month: 'Nov', contributions: 54 },\n    { month: 'Dec', contributions: 38 },\n    { month: 'Jan', contributions: 45 },\n    { month: 'Feb', contributions: 67 },\n    { month: 'Mar', contributions: 52 },\n    { month: 'Apr', contributions: 49 },\n  ];\n\n  const languageData = [\n    { name: 'JavaScript', value: 38, color: '#f1e05a' },\n    { name: 'TypeScript', value: 24, color: '#3178c6' },\n    { name: 'Python', value: 18, color: '#3572A5' },\n    { name: 'HTML', value: 10, color: '#e34c26' },\n    { name: 'CSS', value: 10, color: '#563d7c' },\n  ];\n\n  const repoData = [\n    { name: 'ML-Face-Recognition', stars: 15, forks: 7 },\n    { name: 'React-Portfolio', stars: 12, forks: 5 },\n    { name: 'UI-Component-Library', stars: 8, forks: 3 },\n    { name: 'Python-Data-Analysis', stars: 7, forks: 2 },\n    { name: 'Mobile-App-Template', stars: 5, forks: 1 },\n  ];\n\n  return (\n    <section id=\"stats\" className=\"py-20 bg-github-light\">\n      <div className=\"section-container\">\n        <motion.h2\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n          viewport={{ once: true }}\n          className=\"section-title\"\n        >\n          GitHub Stats\n        </motion.h2>\n\n        <div className=\"mb-10 flex justify-center\">\n          <div className=\"flex rounded-lg overflow-hidden border border-github-border\">\n            <button\n              onClick={() => setActiveTab('contributions')}\n              className={`px-4 py-2 ${activeTab === 'contributions' ? 'bg-neon-green text-black' : 'bg-github-dark text-github-text'}`}\n            >\n              Contributions\n            </button>\n            <button\n              onClick={() => setActiveTab('languages')}\n              className={`px-4 py-2 ${activeTab === 'languages' ? 'bg-neon-green text-black' : 'bg-github-dark text-github-text'}`}\n            >\n              Languages\n            </button>\n            <button\n              onClick={() => setActiveTab('repos')}\n              className={`px-4 py-2 ${activeTab === 'repos' ? 'bg-neon-green text-black' : 'bg-github-dark text-github-text'}`}\n            >\n              Top Repos\n            </button>\n          </div>\n        </div>\n\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, amount: 0.1 }}\n          className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\"\n        >\n          <motion.div variants={itemVariants} className=\"bg-github-dark p-6 rounded-xl border border-github-border\">\n            <h3 className=\"text-xl font-bold text-white mb-6\">Green Hacker's GitHub Stats</h3>\n\n            <div className=\"grid grid-cols-2 gap-4\">\n              <motion.div\n                className=\"p-4 bg-github-light/50 rounded-lg hover:bg-github-light transition-colors\"\n                whileHover={{ y: -5 }}\n              >\n                <p className=\"text-sm text-github-text\">Total Stars Earned:</p>\n                <motion.p\n                  className=\"text-2xl font-bold text-white\"\n                  initial={{ opacity: 0 }}\n                  whileInView={{ opacity: 1 }}\n                  transition={{ duration: 1 }}\n                  viewport={{ once: true }}\n                >\n                  {githubStats.stars}\n                </motion.p>\n              </motion.div>\n              <motion.div\n                className=\"p-4 bg-github-light/50 rounded-lg hover:bg-github-light transition-colors\"\n                whileHover={{ y: -5 }}\n              >\n                <p className=\"text-sm text-github-text\">Total Commits (2025):</p>\n                <motion.p\n                  className=\"text-2xl font-bold text-white\"\n                  initial={{ opacity: 0 }}\n                  whileInView={{ opacity: 1 }}\n                  transition={{ duration: 1 }}\n                  viewport={{ once: true }}\n                >\n                  {githubStats.commits}\n                </motion.p>\n              </motion.div>\n              <motion.div\n                className=\"p-4 bg-github-light/50 rounded-lg hover:bg-github-light transition-colors\"\n                whileHover={{ y: -5 }}\n              >\n                <p className=\"text-sm text-github-text\">Total PRs:</p>\n                <motion.p\n                  className=\"text-2xl font-bold text-white\"\n                  initial={{ opacity: 0 }}\n                  whileInView={{ opacity: 1 }}\n                  transition={{ duration: 1 }}\n                  viewport={{ once: true }}\n                >\n                  {githubStats.prs}\n                </motion.p>\n              </motion.div>\n              <motion.div\n                className=\"p-4 bg-github-light/50 rounded-lg hover:bg-github-light transition-colors\"\n                whileHover={{ y: -5 }}\n              >\n                <p className=\"text-sm text-github-text\">Total Issues:</p>\n                <motion.p\n                  className=\"text-2xl font-bold text-white\"\n                  initial={{ opacity: 0 }}\n                  whileInView={{ opacity: 1 }}\n                  transition={{ duration: 1 }}\n                  viewport={{ once: true }}\n                >\n                  {githubStats.issues}\n                </motion.p>\n              </motion.div>\n              <motion.div\n                className=\"p-4 bg-github-light/50 rounded-lg col-span-2 hover:bg-github-light transition-colors\"\n                whileHover={{ y: -5 }}\n              >\n                <p className=\"text-sm text-github-text\">Contributed to (last year):</p>\n                <motion.p\n                  className=\"text-2xl font-bold text-white\"\n                  initial={{ opacity: 0 }}\n                  whileInView={{ opacity: 1 }}\n                  transition={{ duration: 1 }}\n                  viewport={{ once: true }}\n                >\n                  {githubStats.contributions} Open Source Projects\n                </motion.p>\n              </motion.div>\n            </div>\n\n            <div className=\"mt-8 grid grid-cols-3 gap-4\">\n              <div className=\"p-5 flex flex-col items-center justify-center\">\n                <motion.p\n                  className=\"text-3xl font-bold text-white\"\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5 }}\n                  viewport={{ once: true }}\n                >\n                  623\n                </motion.p>\n                <p className=\"text-sm text-github-text text-center mt-2\">This Year</p>\n                <p className=\"text-xs text-github-text text-center mt-1\">Contributions</p>\n              </div>\n\n              <div className=\"p-5 flex flex-col items-center justify-center\">\n                <motion.div\n                  className=\"w-20 h-20 rounded-full bg-github-light flex items-center justify-center border-4 border-neon-green/70\"\n                  initial={{ scale: 0 }}\n                  whileInView={{ scale: 1 }}\n                  transition={{ duration: 0.5, type: 'spring' }}\n                  viewport={{ once: true }}\n                >\n                  <p className=\"text-3xl font-bold text-white\">3</p>\n                </motion.div>\n                <p className=\"text-sm text-github-text text-center mt-2\">Current Streak</p>\n              </div>\n\n              <div className=\"p-5 flex flex-col items-center justify-center\">\n                <motion.p\n                  className=\"text-3xl font-bold text-white\"\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5 }}\n                  viewport={{ once: true }}\n                >\n                  17\n                </motion.p>\n                <p className=\"text-sm text-github-text text-center mt-2\">Longest Streak</p>\n                <p className=\"text-xs text-github-text text-center mt-1\">Apr 8 - Apr 24</p>\n              </div>\n            </div>\n          </motion.div>\n\n          <motion.div variants={itemVariants} className=\"bg-github-dark p-6 rounded-xl border border-github-border\">\n            {activeTab === 'contributions' && (\n              <>\n                <h3 className=\"text-xl font-bold text-white mb-6\">Contribution Activity</h3>\n\n                <div className=\"h-64\">\n                  <ResponsiveContainer width=\"100%\" height=\"100%\">\n                    <BarChart data={contributionData} margin={{ top: 20, right: 30, left: 0, bottom: 5 }}>\n                      <XAxis dataKey=\"month\" stroke=\"#8b949e\" />\n                      <YAxis stroke=\"#8b949e\" />\n                      <Tooltip\n                        contentStyle={{ backgroundColor: '#161b22', border: '1px solid #30363d' }}\n                        itemStyle={{ color: '#c9d1d9' }}\n                        labelStyle={{ color: 'white', fontWeight: 'bold' }}\n                      />\n                      <Bar dataKey=\"contributions\" fill=\"#3fb950\" radius={[4, 4, 0, 0]}>\n                        {contributionData.map((entry, index) => (\n                          <Cell\n                            key={`cell-${index}`}\n                            fill={entry.contributions > 50 ? '#3fb950' : '#388e3c'}\n                          />\n                        ))}\n                      </Bar>\n                    </BarChart>\n                  </ResponsiveContainer>\n                </div>\n              </>\n            )}\n\n            {activeTab === 'languages' && (\n              <>\n                <h3 className=\"text-xl font-bold text-white mb-6\">Most Used Languages</h3>\n\n                <div className=\"flex\">\n                  <div className=\"h-64 w-1/2\">\n                    <ResponsiveContainer width=\"100%\" height=\"100%\">\n                      <PieChart>\n                        <Pie\n                          data={languageData}\n                          cx=\"50%\"\n                          cy=\"50%\"\n                          innerRadius={60}\n                          outerRadius={80}\n                          paddingAngle={2}\n                          dataKey=\"value\"\n                          label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\n                          labelLine={false}\n                        >\n                          {languageData.map((entry, index) => (\n                            <Cell key={`cell-${index}`} fill={entry.color} />\n                          ))}\n                        </Pie>\n                        <Tooltip\n                          contentStyle={{ backgroundColor: '#161b22', border: '1px solid #30363d' }}\n                          formatter={(value) => [`${value}%`, 'Usage']}\n                        />\n                      </PieChart>\n                    </ResponsiveContainer>\n                  </div>\n\n                  <div className=\"w-1/2 flex flex-col justify-center space-y-4 pl-4\">\n                    {languageData.map((lang, index) => (\n                      <div key={index} className=\"flex items-center\">\n                        <div className=\"w-3 h-3 rounded-full mr-2\" style={{ backgroundColor: lang.color }}></div>\n                        <span className=\"text-white mr-2\">{lang.name}</span>\n                        <span className=\"text-sm text-github-text\">{lang.value}%</span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </>\n            )}\n\n            {activeTab === 'repos' && (\n              <>\n                <h3 className=\"text-xl font-bold text-white mb-6\">Top Repositories</h3>\n\n                <div className=\"space-y-4\">\n                  {repoData.map((repo, index) => (\n                    <motion.div\n                      key={index}\n                      className=\"p-4 bg-github-light/50 rounded-lg hover:bg-github-light cursor-pointer transition-colors\"\n                      whileHover={{ x: 5 }}\n                    >\n                      <div className=\"flex justify-between items-center\">\n                        <h4 className=\"text-neon-green font-medium\">{repo.name}</h4>\n                        <div className=\"flex space-x-3 text-github-text\">\n                          <span className=\"flex items-center text-sm\">\n                            <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\"></path>\n                            </svg>\n                            {repo.stars}\n                          </span>\n                          <span className=\"flex items-center text-sm\">\n                            <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z\"></path>\n                            </svg>\n                            {repo.forks}\n                          </span>\n                        </div>\n                      </div>\n                    </motion.div>\n                  ))}\n                </div>\n              </>\n            )}\n\n            <div className=\"mt-8\">\n              <h4 className=\"text-lg font-semibold text-white mb-4\">Recent Activity</h4>\n              <div className=\"space-y-4\">\n                <motion.div\n                  className=\"flex items-start space-x-3\"\n                  initial={{ opacity: 0, x: -20 }}\n                  whileInView={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.5 }}\n                  viewport={{ once: true }}\n                >\n                  <div className=\"w-6 h-6 bg-github-light rounded-full flex items-center justify-center flex-shrink-0 mt-1\">\n                    <svg className=\"w-4 h-4 text-neon-green\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n                    </svg>\n                  </div>\n                  <div>\n                    <p className=\"text-white\">Created 14 commits in 5 repositories</p>\n                    <p className=\"text-sm text-github-text\">Last week</p>\n                  </div>\n                </motion.div>\n                <motion.div\n                  className=\"flex items-start space-x-3\"\n                  initial={{ opacity: 0, x: -20 }}\n                  whileInView={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.5, delay: 0.2 }}\n                  viewport={{ once: true }}\n                >\n                  <div className=\"w-6 h-6 bg-github-light rounded-full flex items-center justify-center flex-shrink-0 mt-1\">\n                    <svg className=\"w-4 h-4 text-neon-blue\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\"></path>\n                    </svg>\n                  </div>\n                  <div>\n                    <p className=\"text-white\">Created 2 new repositories</p>\n                    <p className=\"text-sm text-github-text\">This month</p>\n                  </div>\n                </motion.div>\n              </div>\n              <motion.button\n                className=\"mt-6 w-full py-2 border border-github-border rounded-md text-github-text hover:bg-github-light transition-colors\"\n                whileHover={{ backgroundColor: 'rgba(255,255,255,0.1)' }}\n                whileTap={{ scale: 0.98 }}\n              >\n                Show more activity\n              </motion.button>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Stats;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,QAAQ;IACZ,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAExC,6DAA6D;IAC7D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,OAAO,EAAE;YACpB,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD;QACnB;IACF,GAAG;QAAC,SAAS,OAAO;KAAC;IAErB,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IAEA,MAAM,cAAc;QAClB,OAAO;QACP,SAAS;QACT,KAAK;QACL,QAAQ;QACR,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB;YAAE,OAAO;YAAO,eAAe;QAAG;QAClC;YAAE,OAAO;YAAO,eAAe;QAAG;QAClC;YAAE,OAAO;YAAO,eAAe;QAAG;QAClC;YAAE,OAAO;YAAO,eAAe;QAAG;QAClC;YAAE,OAAO;YAAO,eAAe;QAAG;QAClC;YAAE,OAAO;YAAO,eAAe;QAAG;QAClC;YAAE,OAAO;YAAO,eAAe;QAAG;QAClC;YAAE,OAAO;YAAO,eAAe;QAAG;QAClC;YAAE,OAAO;YAAO,eAAe;QAAG;QAClC;YAAE,OAAO;YAAO,eAAe;QAAG;QAClC;YAAE,OAAO;YAAO,eAAe;QAAG;QAClC;YAAE,OAAO;YAAO,eAAe;QAAG;KACnC;IAED,MAAM,eAAe;QACnB;YAAE,MAAM;YAAc,OAAO;YAAI,OAAO;QAAU;QAClD;YAAE,MAAM;YAAc,OAAO;YAAI,OAAO;QAAU;QAClD;YAAE,MAAM;YAAU,OAAO;YAAI,OAAO;QAAU;QAC9C;YAAE,MAAM;YAAQ,OAAO;YAAI,OAAO;QAAU;QAC5C;YAAE,MAAM;YAAO,OAAO;YAAI,OAAO;QAAU;KAC5C;IAED,MAAM,WAAW;QACf;YAAE,MAAM;YAAuB,OAAO;YAAI,OAAO;QAAE;QACnD;YAAE,MAAM;YAAmB,OAAO;YAAI,OAAO;QAAE;QAC/C;YAAE,MAAM;YAAwB,OAAO;YAAG,OAAO;QAAE;QACnD;YAAE,MAAM;YAAwB,OAAO;YAAG,OAAO;QAAE;QACnD;YAAE,MAAM;YAAuB,OAAO;YAAG,OAAO;QAAE;KACnD;IAED,qBACE,8OAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC5B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oBACR,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BACX;;;;;;8BAID,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,UAAU,EAAE,cAAc,kBAAkB,6BAA6B,mCAAmC;0CACzH;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,UAAU,EAAE,cAAc,cAAc,6BAA6B,mCAAmC;0CACrH;;;;;;0CAGD,8OAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,UAAU,EAAE,cAAc,UAAU,6BAA6B,mCAAmC;0CACjH;;;;;;;;;;;;;;;;;8BAML,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,UAAU;oBACV,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAI;oBACpC,WAAU;;sCAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;;8CAC5C,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAElD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,YAAY;gDAAE,GAAG,CAAC;4CAAE;;8DAEpB,8OAAC;oDAAE,WAAU;8DAA2B;;;;;;8DACxC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oDACP,WAAU;oDACV,SAAS;wDAAE,SAAS;oDAAE;oDACtB,aAAa;wDAAE,SAAS;oDAAE;oDAC1B,YAAY;wDAAE,UAAU;oDAAE;oDAC1B,UAAU;wDAAE,MAAM;oDAAK;8DAEtB,YAAY,KAAK;;;;;;;;;;;;sDAGtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,YAAY;gDAAE,GAAG,CAAC;4CAAE;;8DAEpB,8OAAC;oDAAE,WAAU;8DAA2B;;;;;;8DACxC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oDACP,WAAU;oDACV,SAAS;wDAAE,SAAS;oDAAE;oDACtB,aAAa;wDAAE,SAAS;oDAAE;oDAC1B,YAAY;wDAAE,UAAU;oDAAE;oDAC1B,UAAU;wDAAE,MAAM;oDAAK;8DAEtB,YAAY,OAAO;;;;;;;;;;;;sDAGxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,YAAY;gDAAE,GAAG,CAAC;4CAAE;;8DAEpB,8OAAC;oDAAE,WAAU;8DAA2B;;;;;;8DACxC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oDACP,WAAU;oDACV,SAAS;wDAAE,SAAS;oDAAE;oDACtB,aAAa;wDAAE,SAAS;oDAAE;oDAC1B,YAAY;wDAAE,UAAU;oDAAE;oDAC1B,UAAU;wDAAE,MAAM;oDAAK;8DAEtB,YAAY,GAAG;;;;;;;;;;;;sDAGpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,YAAY;gDAAE,GAAG,CAAC;4CAAE;;8DAEpB,8OAAC;oDAAE,WAAU;8DAA2B;;;;;;8DACxC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oDACP,WAAU;oDACV,SAAS;wDAAE,SAAS;oDAAE;oDACtB,aAAa;wDAAE,SAAS;oDAAE;oDAC1B,YAAY;wDAAE,UAAU;oDAAE;oDAC1B,UAAU;wDAAE,MAAM;oDAAK;8DAEtB,YAAY,MAAM;;;;;;;;;;;;sDAGvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,YAAY;gDAAE,GAAG,CAAC;4CAAE;;8DAEpB,8OAAC;oDAAE,WAAU;8DAA2B;;;;;;8DACxC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oDACP,WAAU;oDACV,SAAS;wDAAE,SAAS;oDAAE;oDACtB,aAAa;wDAAE,SAAS;oDAAE;oDAC1B,YAAY;wDAAE,UAAU;oDAAE;oDAC1B,UAAU;wDAAE,MAAM;oDAAK;;wDAEtB,YAAY,aAAa;wDAAC;;;;;;;;;;;;;;;;;;;8CAKjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oDACP,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,aAAa;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAChC,YAAY;wDAAE,UAAU;oDAAI;oDAC5B,UAAU;wDAAE,MAAM;oDAAK;8DACxB;;;;;;8DAGD,8OAAC;oDAAE,WAAU;8DAA4C;;;;;;8DACzD,8OAAC;oDAAE,WAAU;8DAA4C;;;;;;;;;;;;sDAG3D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDAAE,OAAO;oDAAE;oDACpB,aAAa;wDAAE,OAAO;oDAAE;oDACxB,YAAY;wDAAE,UAAU;wDAAK,MAAM;oDAAS;oDAC5C,UAAU;wDAAE,MAAM;oDAAK;8DAEvB,cAAA,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;8DAE/C,8OAAC;oDAAE,WAAU;8DAA4C;;;;;;;;;;;;sDAG3D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oDACP,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,aAAa;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAChC,YAAY;wDAAE,UAAU;oDAAI;oDAC5B,UAAU;wDAAE,MAAM;oDAAK;8DACxB;;;;;;8DAGD,8OAAC;oDAAE,WAAU;8DAA4C;;;;;;8DACzD,8OAAC;oDAAE,WAAU;8DAA4C;;;;;;;;;;;;;;;;;;;;;;;;sCAK/D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU;4BAAc,WAAU;;gCAC3C,cAAc,iCACb;;sDACE,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAElD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;gDAAC,OAAM;gDAAO,QAAO;0DACvC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;oDAAC,MAAM;oDAAkB,QAAQ;wDAAE,KAAK;wDAAI,OAAO;wDAAI,MAAM;wDAAG,QAAQ;oDAAE;;sEACjF,8OAAC,qJAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAQ,QAAO;;;;;;sEAC9B,8OAAC,qJAAA,CAAA,QAAK;4DAAC,QAAO;;;;;;sEACd,8OAAC,uJAAA,CAAA,UAAO;4DACN,cAAc;gEAAE,iBAAiB;gEAAW,QAAQ;4DAAoB;4DACxE,WAAW;gEAAE,OAAO;4DAAU;4DAC9B,YAAY;gEAAE,OAAO;gEAAS,YAAY;4DAAO;;;;;;sEAEnD,8OAAC,mJAAA,CAAA,MAAG;4DAAC,SAAQ;4DAAgB,MAAK;4DAAU,QAAQ;gEAAC;gEAAG;gEAAG;gEAAG;6DAAE;sEAC7D,iBAAiB,GAAG,CAAC,CAAC,OAAO,sBAC5B,8OAAC,oJAAA,CAAA,OAAI;oEAEH,MAAM,MAAM,aAAa,GAAG,KAAK,YAAY;mEADxC,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAWnC,cAAc,6BACb;;sDACE,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAElD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;wDAAC,OAAM;wDAAO,QAAO;kEACvC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;;8EACP,8OAAC,+IAAA,CAAA,MAAG;oEACF,MAAM;oEACN,IAAG;oEACH,IAAG;oEACH,aAAa;oEACb,aAAa;oEACb,cAAc;oEACd,SAAQ;oEACR,OAAO,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,GAAK,GAAG,KAAK,CAAC,EAAE,CAAC,UAAU,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;oEACtE,WAAW;8EAEV,aAAa,GAAG,CAAC,CAAC,OAAO,sBACxB,8OAAC,oJAAA,CAAA,OAAI;4EAAuB,MAAM,MAAM,KAAK;2EAAlC,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;8EAG9B,8OAAC,uJAAA,CAAA,UAAO;oEACN,cAAc;wEAAE,iBAAiB;wEAAW,QAAQ;oEAAoB;oEACxE,WAAW,CAAC,QAAU;4EAAC,GAAG,MAAM,CAAC,CAAC;4EAAE;yEAAQ;;;;;;;;;;;;;;;;;;;;;;8DAMpD,8OAAC;oDAAI,WAAU;8DACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC;4DAAgB,WAAU;;8EACzB,8OAAC;oEAAI,WAAU;oEAA4B,OAAO;wEAAE,iBAAiB,KAAK,KAAK;oEAAC;;;;;;8EAChF,8OAAC;oEAAK,WAAU;8EAAmB,KAAK,IAAI;;;;;;8EAC5C,8OAAC;oEAAK,WAAU;;wEAA4B,KAAK,KAAK;wEAAC;;;;;;;;2DAH/C;;;;;;;;;;;;;;;;;;gCAWnB,cAAc,yBACb;;sDACE,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAElD,8OAAC;4CAAI,WAAU;sDACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,WAAU;oDACV,YAAY;wDAAE,GAAG;oDAAE;8DAEnB,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAA+B,KAAK,IAAI;;;;;;0EACtD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;;0FACd,8OAAC;gFAAI,WAAU;gFAAe,MAAK;gFAAO,QAAO;gFAAe,SAAQ;gFAAY,OAAM;0FACxF,cAAA,8OAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAa;oFAAG,GAAE;;;;;;;;;;;4EAEtE,KAAK,KAAK;;;;;;;kFAEb,8OAAC;wEAAK,WAAU;;0FACd,8OAAC;gFAAI,WAAU;gFAAe,MAAK;gFAAO,QAAO;gFAAe,SAAQ;gFAAY,OAAM;0FACxF,cAAA,8OAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAa;oFAAG,GAAE;;;;;;;;;;;4EAEtE,KAAK,KAAK;;;;;;;;;;;;;;;;;;;mDAjBZ;;;;;;;;;;;;8CA2Bf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,GAAG,CAAC;oDAAG;oDAC9B,aAAa;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAChC,YAAY;wDAAE,UAAU;oDAAI;oDAC5B,UAAU;wDAAE,MAAM;oDAAK;;sEAEvB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAA0B,MAAK;gEAAO,QAAO;gEAAe,SAAQ;gEAAY,OAAM;0EACnG,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;sEAGzE,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAa;;;;;;8EAC1B,8OAAC;oEAAE,WAAU;8EAA2B;;;;;;;;;;;;;;;;;;8DAG5C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,GAAG,CAAC;oDAAG;oDAC9B,aAAa;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAChC,YAAY;wDAAE,UAAU;wDAAK,OAAO;oDAAI;oDACxC,UAAU;wDAAE,MAAM;oDAAK;;sEAEvB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAAyB,MAAK;gEAAO,QAAO;gEAAe,SAAQ;gEAAY,OAAM;0EAClG,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;sEAGzE,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAa;;;;;;8EAC1B,8OAAC;oEAAE,WAAU;8EAA2B;;;;;;;;;;;;;;;;;;;;;;;;sDAI9C,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,WAAU;4CACV,YAAY;gDAAE,iBAAiB;4CAAwB;4CACvD,UAAU;gDAAE,OAAO;4CAAK;sDACzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 6112, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/Contact.tsx"], "sourcesContent": ["\n'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { useToast } from \"@/hooks/use-toast\";\n\nconst Contact = () => {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: ''\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const { toast } = useToast();\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({ ...prev, [name]: value }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n\n    try {\n      const response = await fetch('/api/contact', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      });\n\n      const data = await response.json();\n\n      if (response.ok) {\n        toast({\n          title: \"Message sent\",\n          description: \"Thanks for reaching out! I'll get back to you soon.\",\n        });\n        setFormData({\n          name: '',\n          email: '',\n          subject: '',\n          message: ''\n        });\n      } else {\n        toast({\n          title: \"Error\",\n          description: data.error || \"Failed to send message. Please try again.\",\n          variant: \"destructive\",\n        });\n      }\n    } catch (error) {\n      toast({\n        title: \"Error\",\n        description: \"Failed to send message. Please check your connection and try again.\",\n        variant: \"destructive\",\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <section id=\"contact\" className=\"py-20 bg-github-dark\">\n      <div className=\"section-container\">\n        <motion.h2\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n          viewport={{ once: true }}\n          className=\"section-title\"\n        >\n          Contact\n        </motion.h2>\n\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.2 }}\n          viewport={{ once: true }}\n          className=\"text-center max-w-xl mx-auto mb-12\"\n        >\n          <p className=\"text-lg text-github-text\">\n            Feel free to reach out if you'd like to collaborate, discuss tech, or share some awesome ideas!\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n          <motion.div\n            initial={{ opacity: 0, x: -30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.5 }}\n            viewport={{ once: true }}\n            className=\"space-y-6\"\n          >\n            <h3 className=\"text-2xl font-bold text-white\">Get in Touch</h3>\n            <p className=\"text-github-text\">\n              Whether you have a project in mind, a question about my work, or just want to say hi, I'd love to hear from you.\n            </p>\n\n            <div className=\"space-y-4\">\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"w-10 h-10 rounded-full bg-github-light flex items-center justify-center flex-shrink-0\">\n                  <svg className=\"w-5 h-5 text-neon-green\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"></path>\n                  </svg>\n                </div>\n                <div>\n                  <h4 className=\"font-medium text-white\">Email</h4>\n                  <p className=\"text-github-text\"><a href=\"mailto:<EMAIL>\"><EMAIL></a></p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"w-10 h-10 rounded-full bg-github-light flex items-center justify-center flex-shrink-0\">\n                  <svg className=\"w-5 h-5 text-neon-blue\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"></path>\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"></path>\n                  </svg>\n                </div>\n                <div>\n                  <h4 className=\"font-medium text-white\">Location</h4>\n                  <p className=\"text-github-text\">Pune, Maharashtra</p>\n                </div>\n              </div>\n\n              <div className=\"flex items-start space-x-4\">\n                <div className=\"w-10 h-10 rounded-full bg-github-light flex items-center justify-center flex-shrink-0\">\n                  <svg className=\"w-5 h-5 text-neon-purple\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1\"></path>\n                  </svg>\n                </div>\n                <div>\n                  <h4 className=\"font-medium text-white\">Portfolio</h4>\n                  <p className=\"text-github-text\">https://portfolio.greenhacker.tech/</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"pt-8\">\n              <h3 className=\"text-xl font-bold text-white mb-4\">Connect With Me</h3>\n              <div className=\"flex space-x-4\">\n                <a\n                  href=\"https://www.linkedin.com/in/harsh-hirawat-b657061b7/\"\n                  target=\"_blank\"\n                  rel=\"noreferrer\"\n                  className=\"w-12 h-12 rounded-full bg-github-light flex items-center justify-center hover:bg-neon-blue/20 transition-colors\"\n                >\n                  <svg className=\"w-6 h-6 text-neon-blue\" fill=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"/>\n                  </svg>\n                </a>\n                <a\n                  href=\"https://instagram.com/harsh_hirawat\"\n                  target=\"_blank\"\n                  rel=\"noreferrer\"\n                  className=\"w-12 h-12 rounded-full bg-github-light flex items-center justify-center hover:bg-neon-pink/20 transition-colors\"\n                >\n                  <svg className=\"w-6 h-6 text-neon-pink\" fill=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\"/>\n                  </svg>\n                </a>\n                <a\n                  href=\"https://github.com/GreenHacker420\"\n                  target=\"_blank\"\n                  rel=\"noreferrer\"\n                  className=\"w-12 h-12 rounded-full bg-github-light flex items-center justify-center hover:bg-white/10 transition-colors\"\n                >\n                  <svg className=\"w-6 h-6 text-white\" fill=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path d=\"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"/>\n                  </svg>\n                </a>\n              </div>\n            </div>\n          </motion.div>\n\n          <motion.div\n            initial={{ opacity: 0, x: 30 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.5 }}\n            viewport={{ once: true }}\n          >\n            <form onSubmit={handleSubmit} className=\"bg-github-light p-6 rounded-lg border border-github-border\">\n              <h3 className=\"text-2xl font-bold text-white mb-6\">Send a Message</h3>\n\n              <div className=\"space-y-4\">\n                <div>\n                  <label htmlFor=\"name\" className=\"block text-github-text text-sm font-medium mb-2\">\n                    Your Name\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    name=\"name\"\n                    value={formData.name}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 bg-github-dark border border-github-border rounded-lg focus:outline-none focus:ring-2 focus:ring-neon-green/50 text-white\"\n                    placeholder=\"John Doe\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"email\" className=\"block text-github-text text-sm font-medium mb-2\">\n                    Your Email\n                  </label>\n                  <input\n                    type=\"email\"\n                    id=\"email\"\n                    name=\"email\"\n                    value={formData.email}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 bg-github-dark border border-github-border rounded-lg focus:outline-none focus:ring-2 focus:ring-neon-green/50 text-white\"\n                    placeholder=\"<EMAIL>\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"subject\" className=\"block text-github-text text-sm font-medium mb-2\">\n                    Subject\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"subject\"\n                    name=\"subject\"\n                    value={formData.subject}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 bg-github-dark border border-github-border rounded-lg focus:outline-none focus:ring-2 focus:ring-neon-green/50 text-white\"\n                    placeholder=\"Project Collaboration\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"message\" className=\"block text-github-text text-sm font-medium mb-2\">\n                    Message\n                  </label>\n                  <textarea\n                    id=\"message\"\n                    name=\"message\"\n                    value={formData.message}\n                    onChange={handleChange}\n                    rows={5}\n                    className=\"w-full px-4 py-3 bg-github-dark border border-github-border rounded-lg focus:outline-none focus:ring-2 focus:ring-neon-green/50 text-white\"\n                    placeholder=\"Hi there, I'd like to talk about...\"\n                    required\n                  ></textarea>\n                </div>\n\n                <button\n                  type=\"submit\"\n                  disabled={isSubmitting}\n                  className={`w-full py-3 rounded-lg font-medium transition-colors ${\n                    isSubmitting\n                      ? 'bg-github-dark text-github-text cursor-not-allowed'\n                      : 'bg-neon-green text-black hover:bg-neon-green/90'\n                  }`}\n                >\n                  {isSubmitting ? 'Sending...' : 'Send Message'}\n                </button>\n              </div>\n            </form>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Contact;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAJA;;;;;AAMA,MAAM,UAAU;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,SAAS;QACT,SAAS;IACX;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;IACjD;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;gBACA,YAAY;oBACV,MAAM;oBACN,OAAO;oBACP,SAAS;oBACT,SAAS;gBACX;YACF,OAAO;gBACL,MAAM;oBACJ,OAAO;oBACP,aAAa,KAAK,KAAK,IAAI;oBAC3B,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oBACR,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BACX;;;;;;8BAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAE,WAAU;kCAA2B;;;;;;;;;;;8BAK1C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAAgC;;;;;;8CAC9C,8OAAC;oCAAE,WAAU;8CAAmB;;;;;;8CAIhC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAA0B,MAAK;wDAAO,QAAO;wDAAe,SAAQ;wDAAY,OAAM;kEACnG,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAyB;;;;;;sEACvC,8OAAC;4DAAE,WAAU;sEAAmB,cAAA,8OAAC;gEAAE,MAAK;0EAAgC;;;;;;;;;;;;;;;;;;;;;;;sDAI5E,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAyB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;wDAAY,OAAM;;0EAClG,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;0EACrE,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;8DAGzE,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAyB;;;;;;sEACvC,8OAAC;4DAAE,WAAU;sEAAmB;;;;;;;;;;;;;;;;;;sDAIpC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAA2B,MAAK;wDAAO,QAAO;wDAAe,SAAQ;wDAAY,OAAM;kEACpG,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAyB;;;;;;sEACvC,8OAAC;4DAAE,WAAU;sEAAmB;;;;;;;;;;;;;;;;;;;;;;;;8CAKtC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,QAAO;oDACP,KAAI;oDACJ,WAAU;8DAEV,cAAA,8OAAC;wDAAI,WAAU;wDAAyB,MAAK;wDAAe,SAAQ;wDAAY,OAAM;kEACpF,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,8OAAC;oDACC,MAAK;oDACL,QAAO;oDACP,KAAI;oDACJ,WAAU;8DAEV,cAAA,8OAAC;wDAAI,WAAU;wDAAyB,MAAK;wDAAe,SAAQ;wDAAY,OAAM;kEACpF,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,8OAAC;oDACC,MAAK;oDACL,QAAO;oDACP,KAAI;oDACJ,WAAU;8DAEV,cAAA,8OAAC;wDAAI,WAAU;wDAAqB,MAAK;wDAAe,SAAQ;wDAAY,OAAM;kEAChF,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;sCAEvB,cAAA,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,8OAAC;wCAAG,WAAU;kDAAqC;;;;;;kDAEnD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAO,WAAU;kEAAkD;;;;;;kEAGlF,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,IAAI;wDACpB,UAAU;wDACV,WAAU;wDACV,aAAY;wDACZ,QAAQ;;;;;;;;;;;;0DAIZ,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAQ,WAAU;kEAAkD;;;;;;kEAGnF,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,KAAK;wDACrB,UAAU;wDACV,WAAU;wDACV,aAAY;wDACZ,QAAQ;;;;;;;;;;;;0DAIZ,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAU,WAAU;kEAAkD;;;;;;kEAGrF,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,WAAU;wDACV,aAAY;wDACZ,QAAQ;;;;;;;;;;;;0DAIZ,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAU,WAAU;kEAAkD;;;;;;kEAGrF,8OAAC;wDACC,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,MAAM;wDACN,WAAU;wDACV,aAAY;wDACZ,QAAQ;;;;;;;;;;;;0DAIZ,8OAAC;gDACC,MAAK;gDACL,UAAU;gDACV,WAAW,CAAC,qDAAqD,EAC/D,eACI,uDACA,mDACJ;0DAED,eAAe,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjD;uCAEe", "debugId": null}}, {"offset": {"line": 6804, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/ui/dialog.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,kKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,kKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 6935, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/ui/visually-hidden.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as VisuallyHiddenPrimitive from \"@radix-ui/react-visually-hidden\"\n\nconst VisuallyHidden = React.forwardRef<\n  React.ElementRef<typeof VisuallyHiddenPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof VisuallyHiddenPrimitive.Root>\n>(({ ...props }, ref) => (\n  <VisuallyHiddenPrimitive.Root ref={ref} {...props} />\n))\nVisuallyHidden.displayName = VisuallyHiddenPrimitive.Root.displayName\n\nexport { VisuallyHidden }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,GAAG,OAAO,EAAE,oBACf,8OAAC,8KAAA,CAAA,OAA4B;QAAC,KAAK;QAAM,GAAG,KAAK;;;;;;AAEnD,eAAe,WAAW,GAAG,8KAAA,CAAA,OAA4B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 6960, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/resume/ResumePreview.tsx"], "sourcesContent": ["\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Di<PERSON>, DialogContent, DialogTrigger, DialogTitle, DialogDescription } from '@/components/ui/dialog';\nimport { VisuallyHidden } from '@/components/ui/visually-hidden';\nimport { Download, Eye, FileText, X } from 'lucide-react';\n\n// Resume URL - replace with your actual resume URL\nconst RESUME_URL = '/resume.pdf';\n\nconst ResumePreview = () => {\n  const [pdfLoaded, setPdfLoaded] = useState(false);\n  const [iframeKey, setIframeKey] = useState(Date.now()); // Key to force iframe refresh\n\n  // Function to reload the PDF iframe\n  const reloadPdf = () => {\n    setPdfLoaded(false);\n    setIframeKey(Date.now());\n  };\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      whileInView={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n      viewport={{ once: true }}\n      className=\"bg-github-light rounded-lg p-6 border border-github-border card-hover\"\n    >\n      <div className=\"flex items-start justify-between mb-4\">\n        <div className=\"flex items-center\">\n          <FileText className=\"text-neon-green mr-3\" size={24} />\n          <h3 className=\"text-xl font-semibold text-white\">My Resume</h3>\n        </div>\n        <span className=\"px-3 py-1 bg-neon-green/20 text-neon-green text-xs rounded-full\">PDF</span>\n      </div>\n\n      <p className=\"text-github-text mb-6\">\n        Check out my professional experience, skills, and educational background.\n        Download the PDF or view it directly on this page.\n      </p>\n\n      <div className=\"flex flex-wrap gap-3\">\n        <Dialog>\n          <DialogTrigger asChild>\n            <button className=\"flex items-center gap-2 px-4 py-2 bg-neon-green text-black font-medium rounded-md hover:bg-neon-green/90 transition-all\">\n              <Eye size={16} />\n              View Resume\n            </button>\n          </DialogTrigger>\n          <DialogContent className=\"max-w-4xl w-[90vw] h-[90vh] p-0\">\n            <VisuallyHidden>\n              <DialogTitle>Resume Preview</DialogTitle>\n              <DialogDescription>\n                View and download my professional resume in PDF format\n              </DialogDescription>\n            </VisuallyHidden>\n            <div className=\"flex justify-between items-center p-4 border-b border-github-border\">\n              <h4 className=\"font-semibold text-lg\">Resume Preview</h4>\n              <div className=\"flex items-center gap-2\">\n                <a\n                  href={RESUME_URL}\n                  download=\"GREENHACKER_Resume.pdf\"\n                  className=\"flex items-center gap-2 px-3 py-1 bg-neon-green text-black text-sm rounded-md hover:bg-neon-green/90 transition-all\"\n                >\n                  <Download size={14} />\n                  Download\n                </a>\n                <button\n                  onClick={reloadPdf}\n                  className=\"p-1 rounded-full hover:bg-github-border/30 transition-colors\"\n                  title=\"Reload PDF\"\n                >\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"18\" height=\"18\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\">\n                    <path d=\"M21 2v6h-6\"></path>\n                    <path d=\"M3 12a9 9 0 0 1 15-6.7L21 8\"></path>\n                    <path d=\"M3 12a9 9 0 0 0 6.7 15L13 21\"></path>\n                    <path d=\"M14.3 19.1L21 12\"></path>\n                  </svg>\n                </button>\n                <DialogTrigger asChild>\n                  <button className=\"p-1 rounded-full hover:bg-github-border/30 transition-colors\">\n                    <X size={18} />\n                  </button>\n                </DialogTrigger>\n              </div>\n            </div>\n            <div className=\"h-full bg-gray-800 overflow-auto\">\n              <iframe\n                key={iframeKey}\n                src={RESUME_URL}\n                className=\"w-full h-full\"\n                title=\"Resume Preview\"\n                onLoad={() => setPdfLoaded(true)}\n              />\n              {!pdfLoaded && (\n                <div className=\"absolute inset-0 flex items-center justify-center bg-github-dark/80\">\n                  <div className=\"text-center\">\n                    <div className=\"w-16 h-16 border-4 border-neon-green/30 border-t-neon-green rounded-full animate-spin mx-auto mb-4\"></div>\n                    <p className=\"text-github-text\">Loading resume...</p>\n                  </div>\n                </div>\n              )}\n            </div>\n          </DialogContent>\n        </Dialog>\n\n        <a\n          href={RESUME_URL}\n          download=\"GREENHACKER_Resume.pdf\"\n          className=\"flex items-center gap-2 px-4 py-2 bg-transparent border border-neon-green text-neon-green font-medium rounded-md hover:bg-neon-green/10 transition-all\"\n        >\n          <Download size={16} />\n          Download PDF\n        </a>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default ResumePreview;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;;;;;AAEA,mDAAmD;AACnD,MAAM,aAAa;AAEnB,MAAM,gBAAgB;IACpB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,GAAG,KAAK,8BAA8B;IAEtF,oCAAoC;IACpC,MAAM,YAAY;QAChB,aAAa;QACb,aAAa,KAAK,GAAG;IACvB;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;QAAI;QAC5B,UAAU;YAAE,MAAM;QAAK;QACvB,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;gCAAuB,MAAM;;;;;;0CACjD,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;;;;;;;kCAEnD,8OAAC;wBAAK,WAAU;kCAAkE;;;;;;;;;;;;0BAGpF,8OAAC;gBAAE,WAAU;0BAAwB;;;;;;0BAKrC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;;0CACL,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,OAAO;0CACpB,cAAA,8OAAC;oCAAO,WAAU;;sDAChB,8OAAC,gMAAA,CAAA,MAAG;4CAAC,MAAM;;;;;;wCAAM;;;;;;;;;;;;0CAIrB,8OAAC,kIAAA,CAAA,gBAAa;gCAAC,WAAU;;kDACvB,8OAAC,8IAAA,CAAA,iBAAc;;0DACb,8OAAC,kIAAA,CAAA,cAAW;0DAAC;;;;;;0DACb,8OAAC,kIAAA,CAAA,oBAAiB;0DAAC;;;;;;;;;;;;kDAIrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAwB;;;;;;0DACtC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAM;wDACN,UAAS;wDACT,WAAU;;0EAEV,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,MAAM;;;;;;4DAAM;;;;;;;kEAGxB,8OAAC;wDACC,SAAS;wDACT,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC;4DAAI,OAAM;4DAA6B,OAAM;4DAAK,QAAO;4DAAK,SAAQ;4DAAY,MAAK;4DAAO,QAAO;4DAAe,aAAY;4DAAI,eAAc;4DAAQ,gBAAe;;8EACxK,8OAAC;oEAAK,GAAE;;;;;;8EACR,8OAAC;oEAAK,GAAE;;;;;;8EACR,8OAAC;oEAAK,GAAE;;;;;;8EACR,8OAAC;oEAAK,GAAE;;;;;;;;;;;;;;;;;kEAGZ,8OAAC,kIAAA,CAAA,gBAAa;wDAAC,OAAO;kEACpB,cAAA,8OAAC;4DAAO,WAAU;sEAChB,cAAA,8OAAC,4LAAA,CAAA,IAAC;gEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAKjB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAEC,KAAK;gDACL,WAAU;gDACV,OAAM;gDACN,QAAQ,IAAM,aAAa;+CAJtB;;;;;4CAMN,CAAC,2BACA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAE,WAAU;sEAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ5C,8OAAC;wBACC,MAAM;wBACN,UAAS;wBACT,WAAU;;0CAEV,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,MAAM;;;;;;4BAAM;;;;;;;;;;;;;;;;;;;AAMhC;uCAEe", "debugId": null}}, {"offset": {"line": 7330, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/resume/HighlightItem.tsx"], "sourcesContent": ["\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Award, BookOpen, Coffee } from 'lucide-react';\n\ntype HighlightItemProps = {\n  item: {\n    highlight: string;\n    category: string;\n    icon: 'award' | 'book-open' | 'coffee';\n  };\n  index: number;\n  isSelected: boolean;\n  onClick: () => void;\n};\n\nconst HighlightItem = ({ item, index, isSelected, onClick }: HighlightItemProps) => {\n  // Get icon component based on icon name\n  const getIconComponent = (iconName: 'award' | 'book-open' | 'coffee') => {\n    switch (iconName) {\n      case 'award':\n        return <Award className=\"text-neon-green\" size={18} />;\n      case 'book-open':\n        return <BookOpen className=\"text-neon-green\" size={18} />;\n      case 'coffee':\n        return <Coffee className=\"text-neon-green\" size={18} />;\n    }\n  };\n\n  return (\n    <motion.li \n      initial={{ opacity: 0, x: -20 }}\n      whileInView={{ opacity: 1, x: 0 }}\n      transition={{ duration: 0.3, delay: 0.1 * index }}\n      viewport={{ once: true }}\n      className={`flex items-start p-3 rounded-md transition-all cursor-pointer ${\n        isSelected ? 'bg-github-border/30' : 'hover:bg-github-border/10'\n      }`}\n      onClick={onClick}\n    >\n      <div className=\"bg-github-dark/50 p-2 rounded-md mr-3\">\n        {getIconComponent(item.icon)}\n      </div>\n      <div>\n        <div className=\"flex items-center\">\n          <span className=\"text-neon-green text-xs uppercase tracking-wider\">{item.category}</span>\n        </div>\n        <span className=\"text-github-text block mt-1\">{item.highlight}</span>\n        \n        {isSelected && (\n          <motion.div \n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            className=\"mt-3 text-sm text-github-text/80 border-t border-github-border/30 pt-3\"\n          >\n            <p>This highlight showcases expertise in {item.category.toLowerCase()}. \n            Click to collapse this additional information.</p>\n          </motion.div>\n        )}\n      </div>\n    </motion.li>\n  );\n};\n\nexport default HighlightItem;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;;;;AAaA,MAAM,gBAAgB,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAsB;IAC7E,wCAAwC;IACxC,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;oBAAkB,MAAM;;;;;;YAClD,KAAK;gBACH,qBAAO,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;oBAAkB,MAAM;;;;;;YACrD,KAAK;gBACH,qBAAO,8OAAC,sMAAA,CAAA,SAAM;oBAAC,WAAU;oBAAkB,MAAM;;;;;;QACrD;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;QACR,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK,OAAO,MAAM;QAAM;QAChD,UAAU;YAAE,MAAM;QAAK;QACvB,WAAW,CAAC,8DAA8D,EACxE,aAAa,wBAAwB,6BACrC;QACF,SAAS;;0BAET,8OAAC;gBAAI,WAAU;0BACZ,iBAAiB,KAAK,IAAI;;;;;;0BAE7B,8OAAC;;kCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAAoD,KAAK,QAAQ;;;;;;;;;;;kCAEnF,8OAAC;wBAAK,WAAU;kCAA+B,KAAK,SAAS;;;;;;oBAE5D,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBACjC,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAO;wBACtC,WAAU;kCAEV,cAAA,8OAAC;;gCAAE;gCAAuC,KAAK,QAAQ,CAAC,WAAW;gCAAG;;;;;;;;;;;;;;;;;;;;;;;;AAOlF;uCAEe", "debugId": null}}, {"offset": {"line": 7472, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/utils/geminiService.ts"], "sourcesContent": ["\n// Gemini AI Service for generating dynamic content\n// This is a mock service that simulates API calls to Google's Gemini AI\n\ninterface GeminiHighlightResponse {\n  highlight: string;\n  category: string;\n  icon: 'award' | 'book-open' | 'coffee';  // Fixed: strict union type\n}\n\n// Predefined set of highlight templates for simulation purposes\nconst highlightTemplates: GeminiHighlightResponse[] = [\n  {\n    highlight: \"Implemented serverless architecture using AWS Lambda, reducing operational costs by 40%\",\n    category: \"Cloud Architecture\",\n    icon: \"award\"\n  },\n  {\n    highlight: \"Led a team of 5 developers to deliver a complex e-commerce platform in 3 months\",\n    category: \"Leadership\",\n    icon: \"coffee\" \n  },\n  {\n    highlight: \"Authored technical blog posts with over 100,000 monthly readers\",\n    category: \"Content Creation\",\n    icon: \"book-open\"\n  },\n  {\n    highlight: \"Optimized database queries resulting in a 60% performance improvement\",\n    category: \"Performance\",\n    icon: \"award\"\n  },\n  {\n    highlight: \"Designed and implemented a microservices architecture using Docker and Kubernetes\",\n    category: \"DevOps\",\n    icon: \"coffee\"\n  },\n  {\n    highlight: \"Created custom data visualization libraries used by Fortune 500 companies\",\n    category: \"Visualization\",\n    icon: \"book-open\"\n  }\n];\n\n// Simulated Gemini AI API call\nexport const generateResumeHighlight = async (): Promise<GeminiHighlightResponse> => {\n  // In a real implementation, this would make an API call to Gemini\n  // For simulation, we'll randomly select from predefined highlights\n  \n  return new Promise((resolve) => {\n    // Simulate API latency\n    setTimeout(() => {\n      const randomIndex = Math.floor(Math.random() * highlightTemplates.length);\n      resolve(highlightTemplates[randomIndex]);\n    }, 1500);\n  });\n};\n\n// In a real implementation, you would have a function like this:\n/*\nexport const generateResumeHighlightWithGemini = async (apiKey: string, resumeText: string): Promise<GeminiHighlightResponse> => {\n  try {\n    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n        'Authorization': `Bearer ${apiKey}`\n      },\n      body: JSON.stringify({\n        contents: [{\n          parts: [{\n            text: `Based on this resume excerpt, generate one professional highlight that would stand out to recruiters:\n            ${resumeText}\n            \n            Return your response in valid JSON format with these fields:\n            - highlight: A concise professional highlight statement\n            - category: A short category label for this highlight (1-2 words)\n            - icon: One of these values: \"award\", \"book-open\", or \"coffee\"\n            `\n          }]\n        }],\n        generationConfig: {\n          temperature: 0.7,\n          maxOutputTokens: 200,\n        }\n      })\n    });\n\n    const data = await response.json();\n    const jsonResponse = JSON.parse(data.candidates[0].content.parts[0].text);\n    return jsonResponse;\n  } catch (error) {\n    console.error('Error generating highlight with Gemini:', error);\n    throw error;\n  }\n};\n*/\n"], "names": [], "mappings": "AACA,mDAAmD;AACnD,wEAAwE;;;;AAQxE,gEAAgE;AAChE,MAAM,qBAAgD;IACpD;QACE,WAAW;QACX,UAAU;QACV,MAAM;IACR;IACA;QACE,WAAW;QACX,UAAU;QACV,MAAM;IACR;IACA;QACE,WAAW;QACX,UAAU;QACV,MAAM;IACR;IACA;QACE,WAAW;QACX,UAAU;QACV,MAAM;IACR;IACA;QACE,WAAW;QACX,UAAU;QACV,MAAM;IACR;IACA;QACE,WAAW;QACX,UAAU;QACV,MAAM;IACR;CACD;AAGM,MAAM,0BAA0B;IACrC,kEAAkE;IAClE,mEAAmE;IAEnE,OAAO,IAAI,QAAQ,CAAC;QAClB,uBAAuB;QACvB,WAAW;YACT,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,mBAAmB,MAAM;YACxE,QAAQ,kBAAkB,CAAC,YAAY;QACzC,GAAG;IACL;AACF,GAEA,iEAAiE;CACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA", "debugId": null}}, {"offset": {"line": 7565, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/resume/HighlightsList.tsx"], "sourcesContent": ["\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { toast } from 'sonner';\nimport HighlightItem from './HighlightItem';\nimport { generateResumeHighlight } from '../../../utils/geminiService';\n\ninterface GeminiResponse {\n  highlight: string;\n  category: string;\n  icon: 'award' | 'book-open' | 'coffee';\n}\n\nconst HighlightsList = () => {\n  const [highlights, setHighlights] = useState<GeminiResponse[]>([\n    {\n      highlight: \"Full Stack Development with React, Node.js, and TypeScript\",\n      category: \"Development\",\n      icon: \"award\"\n    },\n    {\n      highlight: \"Machine Learning specialization with PyTorch and TensorFlow\",\n      category: \"AI/ML\",\n      icon: \"book-open\"\n    },\n    {\n      highlight: \"5+ years experience working with distributed teams\",\n      category: \"Experience\",\n      icon: \"coffee\"\n    },\n    {\n      highlight: \"Open Source contributor to various GitHub projects\",\n      category: \"Community\",\n      icon: \"award\"\n    },\n    {\n      highlight: \"Conference speaker on AI and web technologies\",\n      category: \"Speaking\",\n      icon: \"book-open\"\n    }\n  ]);\n  const [selectedHighlight, setSelectedHighlight] = useState<number | null>(null);\n  const [isGeneratingHighlight, setIsGeneratingHighlight] = useState(false);\n\n  // Simulate Gemini AI generating a new highlight\n  const generateNewHighlight = async () => {\n    setIsGeneratingHighlight(true);\n    \n    try {\n      // Use our updated geminiService function\n      const newHighlight = await generateResumeHighlight();\n      setHighlights(prev => [...prev, newHighlight]);\n      toast.success(\"New highlight generated by Gemini AI\");\n    } catch (error) {\n      toast.error(\"Failed to generate highlight\");\n      console.error(error);\n    } finally {\n      setIsGeneratingHighlight(false);\n    }\n  };\n\n  return (\n    <motion.div \n      initial={{ opacity: 0, y: 20 }} \n      whileInView={{ opacity: 1, y: 0 }} \n      transition={{ duration: 0.5, delay: 0.2 }}\n      viewport={{ once: true }}\n      className=\"bg-github-light rounded-lg p-6 border border-github-border\"\n    >\n      <div className=\"flex items-center justify-between mb-6\">\n        <h3 className=\"text-xl font-semibold text-white\">Highlights</h3>\n        <button \n          onClick={generateNewHighlight}\n          disabled={isGeneratingHighlight}\n          className=\"px-3 py-1 bg-neon-purple/20 text-neon-purple text-xs rounded-full hover:bg-neon-purple/30 transition-colors flex items-center gap-1\"\n        >\n          {isGeneratingHighlight ? (\n            <>\n              <span className=\"h-2 w-2 bg-neon-purple rounded-full animate-pulse\"></span>\n              <span>Generating...</span>\n            </>\n          ) : (\n            <>\n              <span>+ Generate with Gemini</span>\n            </>\n          )}\n        </button>\n      </div>\n      \n      <ul className=\"space-y-4\">\n        {highlights.map((item, index) => (\n          <HighlightItem \n            key={index}\n            item={item}\n            index={index}\n            isSelected={selectedHighlight === index}\n            onClick={() => setSelectedHighlight(index === selectedHighlight ? null : index)}\n          />\n        ))}\n      </ul>\n    </motion.div>\n  );\n};\n\nexport default HighlightsList;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;;;;;;;AAQA,MAAM,iBAAiB;IACrB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QAC7D;YACE,WAAW;YACX,UAAU;YACV,MAAM;QACR;QACA;YACE,WAAW;YACX,UAAU;YACV,MAAM;QACR;QACA;YACE,WAAW;YACX,UAAU;YACV,MAAM;QACR;QACA;YACE,WAAW;YACX,UAAU;YACV,MAAM;QACR;QACA;YACE,WAAW;YACX,UAAU;YACV,MAAM;QACR;KACD;IACD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnE,gDAAgD;IAChD,MAAM,uBAAuB;QAC3B,yBAAyB;QAEzB,IAAI;YACF,yCAAyC;YACzC,MAAM,eAAe,MAAM,CAAA,GAAA,6HAAA,CAAA,0BAAuB,AAAD;YACjD,cAAc,CAAA,OAAQ;uBAAI;oBAAM;iBAAa;YAC7C,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,yBAAyB;QAC3B;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,aAAa;YAAE,SAAS;YAAG,GAAG;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK,OAAO;QAAI;QACxC,UAAU;YAAE,MAAM;QAAK;QACvB,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBACC,SAAS;wBACT,UAAU;wBACV,WAAU;kCAET,sCACC;;8CACE,8OAAC;oCAAK,WAAU;;;;;;8CAChB,8OAAC;8CAAK;;;;;;;yDAGR;sCACE,cAAA,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAMd,8OAAC;gBAAG,WAAU;0BACX,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC,yJAAA,CAAA,UAAa;wBAEZ,MAAM;wBACN,OAAO;wBACP,YAAY,sBAAsB;wBAClC,SAAS,IAAM,qBAAqB,UAAU,oBAAoB,OAAO;uBAJpE;;;;;;;;;;;;;;;;AAUjB;uCAEe", "debugId": null}}, {"offset": {"line": 7729, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/sections/Resume.tsx"], "sourcesContent": ["\n'use client';\n\nimport React from 'react';\n\nimport ResumePreview from './resume/ResumePreview';\nimport HighlightsList from './resume/HighlightsList';\n\nconst Resume = () => {\n  return (\n    <section id=\"resume\" className=\"py-20 bg-github-dark relative\">\n      <div className=\"section-container\">\n        <h2 className=\"section-title mb-12\">Resume</h2>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n          <ResumePreview />\n          <HighlightsList />\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Resume;\n"], "names": [], "mappings": ";;;;AAKA;AACA;AALA;;;;AAOA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAQ,IAAG;QAAS,WAAU;kBAC7B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAsB;;;;;;8BAEpC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,yJAAA,CAAA,UAAa;;;;;sCACd,8OAAC,0JAAA,CAAA,UAAc;;;;;;;;;;;;;;;;;;;;;;AAKzB;uCAEe", "debugId": null}}, {"offset": {"line": 7792, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/components/common/ErrorBoundary.tsx"], "sourcesContent": ["\nimport React, { Component, ErrorInfo, ReactNode } from \"react\";\n\ninterface ErrorBoundaryProps {\n  fallback?: ReactNode;\n  children: ReactNode;\n}\n\ninterface ErrorBoundaryState {\n  hasError: boolean;\n  error: Error | null;\n}\n\nclass ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props);\n    this.state = {\n      hasError: false,\n      error: null\n    };\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    return {\n      hasError: true,\n      error\n    };\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {\n    console.error(\"ErrorBoundary caught an error:\", error, errorInfo);\n  }\n\n  render() {\n    if (this.state.hasError) {\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n      return (\n        <div className=\"min-h-screen bg-github-dark text-github-text flex items-center justify-center\">\n          <div className=\"max-w-md p-6 bg-github-light rounded-lg\">\n            <h1 className=\"text-2xl font-bold text-white mb-4\">Something went wrong</h1>\n            <p className=\"mb-4\">We're sorry, but something went wrong with the rendering of this page.</p>\n            <button \n              onClick={() => window.location.reload()}\n              className=\"px-4 py-2 bg-neon-green text-black rounded-md hover:bg-neon-green/90\"\n            >\n              Reload page\n            </button>\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n"], "names": [], "mappings": ";;;;AACA;;;AAYA,MAAM,sBAAsB,qMAAA,CAAA,YAAS;IACnC,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YACX,UAAU;YACV,OAAO;QACT;IACF;IAEA,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YACL,UAAU;YACV;QACF;IACF;IAEA,kBAAkB,KAAY,EAAE,SAAoB,EAAQ;QAC1D,QAAQ,KAAK,CAAC,kCAAkC,OAAO;IACzD;IAEA,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC5B;YACA,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,8OAAC;4BAAE,WAAU;sCAAO;;;;;;sCACpB,8OAAC;4BACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;4BACrC,WAAU;sCACX;;;;;;;;;;;;;;;;;QAMT;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 7873, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/personal/portfolio/bloom-interactive-verse/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from \"react\";\nimport { gsap } from 'gsap';\nimport { ScrollTrigger } from 'gsap/ScrollTrigger';\nimport { ScrollToPlugin } from 'gsap/ScrollToPlugin';\nimport Header from \"@/components/layout/Header\";\nimport Footer from \"@/components/layout/Footer\";\nimport Hero from \"@/components/sections/Hero\";\nimport About from \"@/components/sections/About\";\nimport Skills from \"@/components/sections/Skills\";\nimport Projects from \"@/components/sections/Projects\";\nimport Experience from \"@/components/sections/Experience\";\nimport Stats from \"@/components/sections/Stats\";\nimport Contact from \"@/components/sections/Contact\";\nimport Resume from \"@/components/sections/Resume\";\nimport ErrorBoundary from \"@/components/common/ErrorBoundary\";\nimport {\n  initSmoothScrolling,\n  initScrollAnimations,\n  neonFlickerEffect,\n  createParallaxEffect\n} from \"@/utils/animation\";\n\n// Register GSAP plugins\nif (typeof window !== 'undefined') {\n  gsap.registerPlugin(ScrollTrigger, ScrollToPlugin);\n}\n\n// Simple error fallback component\nconst SectionErrorFallback = ({ section }: { section: string }) => {\n  return (\n    <div className=\"py-20 bg-github-dark\">\n      <div className=\"section-container\">\n        <h2 className=\"section-title\">{section}</h2>\n        <div className=\"p-6 bg-github-light rounded-lg border border-github-border\">\n          <p className=\"text-white\">This section could not be loaded.</p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Safe section wrapper with error boundary\nconst SafeSection = ({ children, name }: { children: React.ReactNode; name: string }) => {\n  return (\n    <ErrorBoundary fallback={<SectionErrorFallback section={name} />}>\n      <section id={name.toLowerCase()}>\n        {children}\n      </section>\n    </ErrorBoundary>\n  );\n};\n\nexport default function HomePage() {\n  useEffect(() => {\n    // Only run on client side\n    if (typeof window === 'undefined') return;\n\n    // Initialize GSAP animations\n    initSmoothScrolling();\n    initScrollAnimations();\n    createParallaxEffect();\n\n    // Apply neon flicker effect to specific elements\n    const sectionTitles = document.querySelectorAll('.section-title');\n    sectionTitles.forEach(title => {\n      neonFlickerEffect(title as HTMLElement);\n    });\n\n    // Set up scroll trigger animations for sections\n    const sections = document.querySelectorAll('section');\n    sections.forEach((section) => {\n      // Create section animations\n      gsap.fromTo(\n        section,\n        { opacity: 0.6, y: 50 },\n        {\n          opacity: 1,\n          y: 0,\n          scrollTrigger: {\n            trigger: section,\n            start: \"top bottom-=100\",\n            end: \"top center\",\n            scrub: true\n          }\n        }\n      );\n    });\n\n    // Clean up ScrollTrigger on unmount\n    return () => {\n      ScrollTrigger.getAll().forEach(trigger => trigger.kill());\n    };\n  }, []);\n\n  return (\n    <div className=\"min-h-screen bg-github-dark text-github-text dark:bg-github-dark dark:text-github-text\">\n      <Header />\n      <main>\n        <SafeSection name=\"Hero\">\n          <Hero />\n        </SafeSection>\n        <SafeSection name=\"About\">\n          <About />\n        </SafeSection>\n        <SafeSection name=\"Skills\">\n          <Skills />\n        </SafeSection>\n        <SafeSection name=\"Projects\">\n          <Projects />\n        </SafeSection>\n        <SafeSection name=\"Experience\">\n          <Experience />\n        </SafeSection>\n        <SafeSection name=\"Resume\">\n          <Resume />\n        </SafeSection>\n        <SafeSection name=\"Stats\">\n          <Stats />\n        </SafeSection>\n        <SafeSection name=\"Contact\">\n          <Contact />\n        </SafeSection>\n      </main>\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjBA;;;;;;;;;;;;;;;;;;AAwBA,wBAAwB;AACxB,uCAAmC;;AAEnC;AAEA,kCAAkC;AAClC,MAAM,uBAAuB,CAAC,EAAE,OAAO,EAAuB;IAC5D,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAiB;;;;;;8BAC/B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAa;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,2CAA2C;AAC3C,MAAM,cAAc,CAAC,EAAE,QAAQ,EAAE,IAAI,EAA+C;IAClF,qBACE,8OAAC,6IAAA,CAAA,UAAa;QAAC,wBAAU,8OAAC;YAAqB,SAAS;;;;;;kBACtD,cAAA,8OAAC;YAAQ,IAAI,KAAK,WAAW;sBAC1B;;;;;;;;;;;AAIT;AAEe,SAAS;IACtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0BAA0B;QAC1B,wCAAmC;;QAOnC,iDAAiD;QACjD,MAAM;QAKN,gDAAgD;QAChD,MAAM;IAuBR,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;;kCACC,8OAAC;wBAAY,MAAK;kCAChB,cAAA,8OAAC,sIAAA,CAAA,UAAI;;;;;;;;;;kCAEP,8OAAC;wBAAY,MAAK;kCAChB,cAAA,8OAAC,uIAAA,CAAA,UAAK;;;;;;;;;;kCAER,8OAAC;wBAAY,MAAK;kCAChB,cAAA,8OAAC,wIAAA,CAAA,UAAM;;;;;;;;;;kCAET,8OAAC;wBAAY,MAAK;kCAChB,cAAA,8OAAC,0IAAA,CAAA,UAAQ;;;;;;;;;;kCAEX,8OAAC;wBAAY,MAAK;kCAChB,cAAA,8OAAC,4IAAA,CAAA,UAAU;;;;;;;;;;kCAEb,8OAAC;wBAAY,MAAK;kCAChB,cAAA,8OAAC,wIAAA,CAAA,UAAM;;;;;;;;;;kCAET,8OAAC;wBAAY,MAAK;kCAChB,cAAA,8OAAC,uIAAA,CAAA,UAAK;;;;;;;;;;kCAER,8OAAC;wBAAY,MAAK;kCAChB,cAAA,8OAAC,yIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;0BAGZ,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}]}