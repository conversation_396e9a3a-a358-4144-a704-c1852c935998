<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" href="/_next/static/media/e4af272ccee01ff0-s.p.woff2" as="font" crossorigin="" type="font/woff2"/><link rel="preload" as="image" href="https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?crop=entropy&amp;cs=tinysrgb&amp;fit=max&amp;fm=jpg&amp;ixid=MnwxfDB8MXxyYW5kb218MHx8dGVjaHx8fHx8fDE2MjM2MzYyODE&amp;ixlib=rb-1.2.1&amp;q=80&amp;utm_campaign=api-credit&amp;utm_medium=referral&amp;utm_source=unsplash_source&amp;w=1080"/><link rel="preload" as="image" href="https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?crop=entropy&amp;cs=tinysrgb&amp;fit=crop&amp;fm=jpg&amp;h=800&amp;ixid=MnwxfDB8MXxyYW5kb218MHx8dGVjaHx8fHx8fDE2MjM2MzYyODE&amp;ixlib=rb-1.2.1&amp;q=80&amp;utm_campaign=api-credit&amp;utm_medium=referral&amp;utm_source=unsplash_source&amp;w=1200"/><link rel="preload" as="image" href="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?crop=entropy&amp;cs=tinysrgb&amp;fit=crop&amp;fm=jpg&amp;h=800&amp;ixid=MnwxfDB8MXxyYW5kb218MHx8dGVjaHx8fHx8fDE2MjM2MzYyODE&amp;ixlib=rb-1.2.1&amp;q=80&amp;utm_campaign=api-credit&amp;utm_medium=referral&amp;utm_source=unsplash_source&amp;w=1200"/><link rel="preload" as="image" href="https://images.unsplash.com/photo-1461749280684-dccba630e2f6?crop=entropy&amp;cs=tinysrgb&amp;fit=crop&amp;fm=jpg&amp;h=800&amp;ixid=MnwxfDB8MXxyYW5kb218MHx8cHJvZ3JhbW1pbmd8fHx8fHwxNjIzNjM2MzU4&amp;ixlib=rb-1.2.1&amp;q=80&amp;utm_campaign=api-credit&amp;utm_medium=referral&amp;utm_source=unsplash_source&amp;w=1200"/><link rel="preload" as="image" href="https://images.unsplash.com/photo-1498050108023-c5249f4df085?crop=entropy&amp;cs=tinysrgb&amp;fit=crop&amp;fm=jpg&amp;h=800&amp;ixid=MnwxfDB8MXxyYW5kb218MHx8Y29kZXx8fHx8fDE2MjM2MzYzNzg&amp;ixlib=rb-1.2.1&amp;q=80&amp;utm_campaign=api-credit&amp;utm_medium=referral&amp;utm_source=unsplash_source&amp;w=1200"/><link rel="stylesheet" href="/_next/static/css/d6541876f5d75465.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-18b4e876baa90827.js"/><script src="/_next/static/chunks/4bd1b696-17c24d8a2b0eb8e1.js" async=""></script><script src="/_next/static/chunks/684-b6b4a4da43dbbeef.js" async=""></script><script src="/_next/static/chunks/main-app-ed4720d79282bffb.js" async=""></script><script src="/_next/static/chunks/710-276d87e630819ab8.js" async=""></script><script src="/_next/static/chunks/107-a403c883544f12ca.js" async=""></script><script src="/_next/static/chunks/469-d5d7bcc5f3e29139.js" async=""></script><script src="/_next/static/chunks/app/layout-1ce3a74b862cc1a7.js" async=""></script><script src="/_next/static/chunks/390-483a04468f2201f1.js" async=""></script><script src="/_next/static/chunks/app/not-found-2db60d607a4c36d9.js" async=""></script><script src="/_next/static/chunks/c6a54c64-faf36295803f59fc.js" async=""></script><script src="/_next/static/chunks/b1644e8c-9e7d08c5b2aa5e05.js" async=""></script><script src="/_next/static/chunks/c15bf2b0-7f3f4bd25724833d.js" async=""></script><script src="/_next/static/chunks/394-db3255639cb235d7.js" async=""></script><script src="/_next/static/chunks/app/page-47dc305994e39189.js" async=""></script><meta name="next-size-adjust" content=""/><link rel="manifest" href="/site.webmanifest"/><meta name="theme-color" content="#0d1117"/><title>GREENHACKER | Developer Portfolio</title><meta name="description" content="Full-stack developer portfolio showcasing modern web technologies, AI integration, and innovative projects."/><meta name="author" content="GreenHacker"/><meta name="keywords" content="developer,portfolio,React,Next.js,TypeScript,AI,machine learning"/><meta name="creator" content="GreenHacker"/><meta name="publisher" content="GreenHacker"/><meta name="robots" content="index, follow"/><meta name="googlebot" content="index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"/><meta name="google-site-verification" content="your-google-verification-code"/><meta property="og:title" content="GREENHACKER | Developer Portfolio"/><meta property="og:description" content="Full-stack developer portfolio showcasing modern web technologies, AI integration, and innovative projects."/><meta property="og:url" content="https://greenhacker.dev"/><meta property="og:site_name" content="GreenHacker Portfolio"/><meta property="og:locale" content="en_US"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:creator" content="@greenhacker"/><meta name="twitter:title" content="GREENHACKER | Developer Portfolio"/><meta name="twitter:description" content="Full-stack developer portfolio showcasing modern web technologies, AI integration, and innovative projects."/><link rel="shortcut icon" href="/logo.jpg"/><link rel="icon" href="/logo.jpg"/><link rel="apple-touch-icon" href="/logo.jpg"/><script>document.querySelectorAll('body link[rel="icon"], body link[rel="apple-touch-icon"]').forEach(el => document.head.appendChild(el))</script><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__className_e8ce0c"><div role="region" aria-label="Notifications (F8)" tabindex="-1" style="pointer-events:none"><ol tabindex="-1" class="fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]"></ol></div><section aria-label="Notifications alt+T" tabindex="-1" aria-live="polite" aria-relevant="additions text" aria-atomic="false"></section><div class="fixed inset-0 bg-black flex items-center justify-center z-50" style="opacity:1"><div class="w-full max-w-3xl bg-black border border-neon-green p-6 rounded-md shadow-neon-green terminal-window" style="opacity:0"><div class="terminal-header flex items-center justify-between mb-4"><div class="text-neon-green font-mono text-sm">~/green-hacker/portfolio</div><div class="flex space-x-2"><div class="w-3 h-3 rounded-full bg-red-500"></div><div class="w-3 h-3 rounded-full bg-yellow-500"></div><div class="w-3 h-3 rounded-full bg-green-500"></div></div></div><div class="terminal-content space-y-2 font-mono text-sm overflow-hidden"><div class="line"><span class="text-neon-blue">$ </span><span class="text-white">load portfolio --env=production --secure</span></div><div class="line text-neon-green" style="opacity:0">Initializing system...<!-- -->▋</div><div class="line" style="opacity:0"><div class="text-github-text">Progress: <!-- -->0<!-- -->%</div><div class="w-full bg-github-dark rounded-full h-2 mt-1"><div class="h-2 rounded-full bg-neon-green" style="width:0px"></div></div></div></div><div class="ascii-art mt-8 text-neon-green font-mono text-xs whitespace-pre"> ██████╗ ██████╗ ███████╗███████╗███╗   ██╗██╗  ██╗ █████╗  ██████╗██╗  ██╗███████╗██████╗
██╔════╝ ██╔══██╗██╔════╝██╔════╝████╗  ██║██║  ██║██╔══██╗██╔════╝██║ ██╔╝██╔════╝██╔══██╗
██║  ███╗██████╔╝█████╗  █████╗  ██╔██╗ ██║███████║███████║██║     █████╔╝ █████╗  ██████╔╝
██║   ██║██╔══██╗██╔══╝  ██╔══╝  ██║╚██╗██║██╔══██║██╔══██║██║     ██╔═██╗ ██╔══╝  ██╔══██╗
╚██████╔╝██║  ██║███████╗███████╗██║ ╚████║██║  ██║██║  ██║╚██████╗██║  ██╗███████╗██║  ██║
 ╚═════╝ ╚═╝  ╚═╝╚══════╝╚══════╝╚═╝  ╚═══╝╚═╝  ╚═╝╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝╚══════╝╚═╝  ╚═╝</div></div><style>
        .terminal-window {
          box-shadow: 0 0 10px rgba(63, 185, 80, 0.3), 0 0 20px rgba(63, 185, 80, 0.2);
        }

        @keyframes scan {
          from { top: 0; }
          to { top: 100%; }
        }

        .terminal-window::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 3px;
          background-color: rgba(63, 185, 80, 0.5);
          animation: scan 3s linear infinite;
        }
      </style></div><div class="fixed inset-0 pointer-events-none z-0 overflow-hidden"><div class="absolute inset-0 opacity-20" style="transform:none"><div class="absolute top-0 -left-4 w-[50vw] h-[50vw] bg-neon-purple rounded-full mix-blend-screen filter blur-[100px] opacity-70"></div><div class="absolute top-[30%] -right-[10%] w-[40vw] h-[40vw] bg-neon-green rounded-full mix-blend-screen filter blur-[100px] opacity-70"></div><div class="absolute -bottom-[20%] left-[20%] w-[60vw] h-[60vw] bg-neon-blue rounded-full mix-blend-screen filter blur-[100px] opacity-40"></div></div></div><div class="fixed top-0 left-0 w-8 h-8 rounded-full border border-neon-green z-[9999] pointer-events-none"></div><div class="fixed top-0 left-0 w-2 h-2 bg-neon-green rounded-full z-[10000] pointer-events-none"></div><div class="min-h-screen bg-github-dark text-github-text dark:bg-github-dark dark:text-github-text"><header class="fixed top-0 w-full z-50 transition-all duration-300 bg-transparent" style="transform:translateY(-100px)"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"><div class="flex justify-between items-center py-4"><a class="flex items-center space-x-2" href="/"><div class="w-10 h-10 rounded-full bg-gradient-to-br from-neon-green to-neon-blue flex items-center justify-center"><span class="font-mono font-bold text-white">GH</span></div><span class="font-bold text-xl">GreenHacker</span></a><nav class="hidden md:flex space-x-8"><a href="#about" class="text-github-text hover:text-white transition-colors group relative"><span>About</span><span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-neon-green transition-all group-hover:w-full"></span></a><a href="#skills" class="text-github-text hover:text-white transition-colors group relative"><span>Skills</span><span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-neon-green transition-all group-hover:w-full"></span></a><a href="#projects" class="text-github-text hover:text-white transition-colors group relative"><span>Projects</span><span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-neon-green transition-all group-hover:w-full"></span></a><a href="#experience" class="text-github-text hover:text-white transition-colors group relative"><span>Experience</span><span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-neon-green transition-all group-hover:w-full"></span></a><a href="#contact" class="text-github-text hover:text-white transition-colors group relative"><span>Contact</span><span class="absolute -bottom-1 left-0 w-0 h-0.5 bg-neon-green transition-all group-hover:w-full"></span></a></nav><button class="md:hidden text-gray-300 hover:text-white" aria-label="Toggle menu"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg></button></div></div><div class="md:hidden overflow-hidden" style="height:0px;opacity:0"><div class="px-4 py-2 space-y-1 bg-github-light border-t border-github-border"><a href="#about" class="block py-2 px-4 text-github-text hover:bg-github-dark hover:text-white rounded-md">About</a><a href="#skills" class="block py-2 px-4 text-github-text hover:bg-github-dark hover:text-white rounded-md">Skills</a><a href="#projects" class="block py-2 px-4 text-github-text hover:bg-github-dark hover:text-white rounded-md">Projects</a><a href="#experience" class="block py-2 px-4 text-github-text hover:bg-github-dark hover:text-white rounded-md">Experience</a><a href="#contact" class="block py-2 px-4 text-github-text hover:bg-github-dark hover:text-white rounded-md">Contact</a></div></div></header><main><section id="hero"><section id="home" class="relative flex items-center overflow-hidden"><div class="absolute inset-0 bg-github-darker z-0 opacity-80"><div class="absolute inset-0 opacity-30"><div class="absolute top-0 -left-4 w-72 h-72 bg-neon-purple rounded-full mix-blend-screen filter blur-xl opacity-70 animate-float"></div><div class="absolute top-8 -right-4 w-72 h-72 bg-neon-green rounded-full mix-blend-screen filter blur-xl opacity-70 animate-float" style="animation-delay:2s"></div><div class="absolute -bottom-8 left-20 w-72 h-72 bg-neon-blue rounded-full mix-blend-screen filter blur-xl opacity-70 animate-float" style="animation-delay:4s"></div></div></div><div class="section-container relative z-10"><div class="max-w-3xl"><div class="overflow-hidden" style="opacity:0;transform:translateY(20px)"><h2 class="text-neon-green text-lg md:text-xl font-mono mb-2 flex items-center"><span class="wave-emoji mr-2 inline-block">👋</span> Hello, I&#x27;m</h2></div><div style="opacity:0;transform:translateY(20px)"><h1 class="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-3 relative">Green Hacker<span class="absolute -bottom-2 left-0 h-1 bg-neon-green w-0 animate-expand"></span></h1></div><div style="opacity:0;transform:translateY(20px)"><h2 class="text-xl md:text-2xl text-github-text font-medium mb-6 flex flex-wrap gap-2 md:gap-4"><span class="flex items-center"><span class="bg-neon-green w-2 h-2 rounded-full mr-2"></span>Full Stack Developer</span><span class="flex items-center"><span class="bg-neon-purple w-2 h-2 rounded-full mr-2"></span>ML Expert</span><span class="flex items-center"><span class="bg-neon-blue w-2 h-2 rounded-full mr-2"></span>OSS Contributor</span></h2></div></div><div class="mt-8" style="opacity:0;transform:translateY(20px)"><p class="text-lg text-github-text max-w-2xl mb-8 leading-relaxed">I&#x27;m currently working on a photo-sharing platform with face recognition. Passionate about open-source and applying Machine Learning to solve real-world problems.</p><div class="flex flex-wrap gap-4"><a href="#projects" class="px-6 py-3 bg-neon-green text-black font-medium rounded-md hover:bg-neon-green/90 transition-colors" tabindex="0">View Projects</a><a href="#contact" class="px-6 py-3 bg-transparent border border-neon-green text-neon-green font-medium rounded-md hover:bg-neon-green/10 transition-colors" tabindex="0">Contact Me</a><a href="#resume" class="px-6 py-3 bg-transparent border border-neon-purple text-neon-purple font-medium rounded-md hover:bg-neon-purple/10 transition-colors" tabindex="0">View Resume</a></div></div><div class="mt-12" style="opacity:0"><p class="text-github-text text-lg flex items-center"><span class="mr-2">Currently:</span><span class="text-neon-green font-mono relative"><span class="absolute inset-y-0 right-[-0.7ch] w-[0.5ch] bg-neon-green animate-cursor-blink"></span></span></p></div></div><div class="absolute bottom-10 left-1/2 transform -translate-x-1/2 z-10" style="opacity:0;transform:translateY(-20px)"><a href="#about" class="flex flex-col items-center text-github-text hover:text-white transition-colors"><span class="mb-2 text-sm">Scroll Down</span><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="animate-bounce"><path d="M12 5v14"></path><path d="m19 12-7 7-7-7"></path></svg></a></div><style>
        @keyframes expand {
          to { width: 100%; }
        }

        .animate-expand {
          animation: expand 1.5s ease-out forwards;
          animation-delay: 0.8s;
        }

        .wave-emoji {
          animation: wave 2.5s infinite;
          transform-origin: 70% 70%;
          display: inline-block;
        }

        @keyframes wave {
          0% { transform: rotate(0deg); }
          10% { transform: rotate(14deg); }
          20% { transform: rotate(-8deg); }
          30% { transform: rotate(14deg); }
          40% { transform: rotate(-4deg); }
          50% { transform: rotate(10deg); }
          60% { transform: rotate(0deg); }
          100% { transform: rotate(0deg); }
        }

        .typewriter {
          overflow: hidden;
          border-right: .15em solid #3fb950;
          white-space: nowrap;
          margin: 0 auto;
          letter-spacing: .15em;
          animation: typing 3.5s steps(40, end), blink-caret .75s step-end infinite;
        }

        @keyframes typing {
          from { width: 0 }
          to { width: 100% }
        }

        @keyframes blink-caret {
          from, to { border-color: transparent }
          50% { border-color: #3fb950 }
        }

        @keyframes float {
          0% { transform: translateY(0px); }
          50% { transform: translateY(-20px); }
          100% { transform: translateY(0px); }
        }

        .animate-float {
          animation: float 6s ease-in-out infinite;
        }
      </style></section></section><section id="about"><section id="about" class="bg-github-light py-20"><div class="section-container" style="opacity:0"><h2 class="section-title" style="opacity:0;transform:translateY(20px)">About Me</h2><div class="grid grid-cols-1 md:grid-cols-3 gap-8"><div class="md:col-span-2 space-y-4" style="opacity:0;transform:translateY(20px)"><p class="text-lg">🚀 I&#x27;m currently working on a photo-sharing platform with face recognition.</p><p class="text-lg">👐 Open to contributing to the open-source community.</p><p class="text-lg">🧠 Learning Machine Learning for face detection.</p><p class="text-lg">💻 Passionate developer and open-source contributor.</p><p class="text-lg">⚡ Fun fact: I can spend hours debugging code but still forget where I kept my phone! 😄</p><div class="pt-4"><h3 class="text-xl font-semibold mb-2">Call to Action:</h3><p class="text-lg">Feel free to reach out if you&#x27;d like to collaborate, discuss tech, or share some awesome ideas!</p></div></div><div class="md:col-span-1" style="opacity:0;transform:translateY(20px)"><div class="bg-github-dark border border-github-border rounded-2xl overflow-hidden card-hover"><div class="aspect-square w-full relative overflow-hidden"><div class="absolute inset-0 bg-gradient-to-br from-neon-green/20 to-neon-purple/20 z-10"></div><img src="https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?crop=entropy&amp;cs=tinysrgb&amp;fit=max&amp;fm=jpg&amp;ixid=MnwxfDB8MXxyYW5kb218MHx8dGVjaHx8fHx8fDE2MjM2MzYyODE&amp;ixlib=rb-1.2.1&amp;q=80&amp;utm_campaign=api-credit&amp;utm_medium=referral&amp;utm_source=unsplash_source&amp;w=1080" alt="Code on screen" class="w-full h-full object-cover object-center"/><div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-github-dark to-transparent h-1/3 z-20"></div></div><div class="p-6"><h3 class="text-xl font-bold mb-2">Socials:</h3><div class="space-y-2"><a href="https://instagram.com" class="flex items-center space-x-2 text-github-text hover:text-neon-pink transition-colors" target="_blank" rel="noopener noreferrer"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"></path></svg><span>Instagram</span></a><a href="https://linkedin.com" class="flex items-center space-x-2 text-github-text hover:text-neon-blue transition-colors" target="_blank" rel="noopener noreferrer"><svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"></path></svg><span>LinkedIn</span></a><a href="mailto:<EMAIL>" class="flex items-center space-x-2 text-github-text hover:text-white transition-colors"><svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path></svg><span>Email</span></a></div></div></div></div></div></div></section></section><section id="skills"><section id="skills" class="py-20 bg-github-dark"><div class="section-container relative"><h2 class="section-title" style="opacity:0;transform:translateY(20px)">Skills</h2><div class="flex justify-end mb-6"><button class="text-sm bg-github-light/30 px-4 py-2 rounded-md text-neon-green hover:bg-github-light/50 transition-colors">Switch to Keyboard View</button></div><div class="mb-10"><div style="opacity:0;transform:translateY(20px)"><div dir="ltr" data-orientation="horizontal" class="w-full"><div role="tablist" aria-orientation="horizontal" class="h-10 items-center justify-center rounded-md p-1 text-muted-foreground flex flex-wrap mb-6 bg-github-light/20" tabindex="-1" data-orientation="horizontal" style="outline:none"><button type="button" role="tab" aria-selected="true" aria-controls="radix-«R4ssflb»-content-Programming Languages" data-state="active" id="radix-«R4ssflb»-trigger-Programming Languages" class="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm data-[state=active]:bg-neon-green data-[state=active]:text-black" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">Programming Languages</button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R4ssflb»-content-Frontend Development" data-state="inactive" id="radix-«R4ssflb»-trigger-Frontend Development" class="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm data-[state=active]:bg-neon-green data-[state=active]:text-black" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">Frontend Development</button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R4ssflb»-content-Backend Development" data-state="inactive" id="radix-«R4ssflb»-trigger-Backend Development" class="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm data-[state=active]:bg-neon-green data-[state=active]:text-black" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">Backend Development</button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R4ssflb»-content-Cloud &amp; Deployment" data-state="inactive" id="radix-«R4ssflb»-trigger-Cloud &amp; Deployment" class="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm data-[state=active]:bg-neon-green data-[state=active]:text-black" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">Cloud &amp; Deployment</button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R4ssflb»-content-Databases" data-state="inactive" id="radix-«R4ssflb»-trigger-Databases" class="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm data-[state=active]:bg-neon-green data-[state=active]:text-black" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">Databases</button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R4ssflb»-content-DevOps &amp; Tools" data-state="inactive" id="radix-«R4ssflb»-trigger-DevOps &amp; Tools" class="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm data-[state=active]:bg-neon-green data-[state=active]:text-black" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">DevOps &amp; Tools</button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R4ssflb»-content-Data Science &amp; ML" data-state="inactive" id="radix-«R4ssflb»-trigger-Data Science &amp; ML" class="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm data-[state=active]:bg-neon-green data-[state=active]:text-black" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">Data Science &amp; ML</button><button type="button" role="tab" aria-selected="false" aria-controls="radix-«R4ssflb»-content-UI/UX &amp; Design" data-state="inactive" id="radix-«R4ssflb»-trigger-UI/UX &amp; Design" class="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm data-[state=active]:bg-neon-green data-[state=active]:text-black" tabindex="-1" data-orientation="horizontal" data-radix-collection-item="">UI/UX &amp; Design</button></div><div data-state="active" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R4ssflb»-trigger-Programming Languages" id="radix-«R4ssflb»-content-Programming Languages" tabindex="0" class="mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2" style="animation-duration:0s"><div class="bg-github-light/20 rounded-lg p-4 mb-6"><h3 class="text-xl text-white font-medium mb-2">Programming Languages</h3><p class="text-github-text">Core programming languages I use for development</p></div><div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 skills-grid"><div id="skill-c---0" class="bg-github-light rounded-lg p-4 border border-github-border skill-item transition-all duration-300" style="opacity:0;transform:translateY(20px)"><div class="flex items-center gap-3 mb-3"><div class="skill-icon flex items-center justify-center p-2 rounded-md bg-github-darker"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#c9d1d9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-code-xml skill-icon-svg"><path d="m18 16 4-4-4-4"></path><path d="m6 8-4 4 4 4"></path><path d="m14.5 4-5 16"></path></svg></div><div class="flex justify-between items-center w-full"><span class="text-white font-medium">C++</span><span class="text-sm text-neon-green">85<!-- -->%</span></div></div><div class="w-full bg-github-dark rounded-full h-2.5"><div class="h-2.5 rounded-full bg-blue-600" style="width:0px"></div></div></div><div id="skill-dart-1" class="bg-github-light rounded-lg p-4 border border-github-border skill-item transition-all duration-300" style="opacity:0;transform:translateY(20px)"><div class="flex items-center gap-3 mb-3"><div class="skill-icon flex items-center justify-center p-2 rounded-md bg-github-darker"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#c9d1d9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-code skill-icon-svg"><polyline points="16 18 22 12 16 6"></polyline><polyline points="8 6 2 12 8 18"></polyline></svg></div><div class="flex justify-between items-center w-full"><span class="text-white font-medium">DART</span><span class="text-sm text-neon-green">70<!-- -->%</span></div></div><div class="w-full bg-github-dark rounded-full h-2.5"><div class="h-2.5 rounded-full bg-blue-400" style="width:0px"></div></div></div><div id="skill-javascript-2" class="bg-github-light rounded-lg p-4 border border-github-border skill-item transition-all duration-300" style="opacity:0;transform:translateY(20px)"><div class="flex items-center gap-3 mb-3"><div class="skill-icon flex items-center justify-center p-2 rounded-md bg-github-darker"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#c9d1d9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-code skill-icon-svg"><polyline points="16 18 22 12 16 6"></polyline><polyline points="8 6 2 12 8 18"></polyline></svg></div><div class="flex justify-between items-center w-full"><span class="text-white font-medium">JAVASCRIPT</span><span class="text-sm text-neon-green">95<!-- -->%</span></div></div><div class="w-full bg-github-dark rounded-full h-2.5"><div class="h-2.5 rounded-full bg-yellow-500" style="width:0px"></div></div></div><div id="skill-python-3" class="bg-github-light rounded-lg p-4 border border-github-border skill-item transition-all duration-300" style="opacity:0;transform:translateY(20px)"><div class="flex items-center gap-3 mb-3"><div class="skill-icon flex items-center justify-center p-2 rounded-md bg-github-darker"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#c9d1d9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-code skill-icon-svg"><path d="M10 12.5 8 15l2 2.5"></path><path d="m14 12.5 2 2.5-2 2.5"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7z"></path></svg></div><div class="flex justify-between items-center w-full"><span class="text-white font-medium">PYTHON</span><span class="text-sm text-neon-green">90<!-- -->%</span></div></div><div class="w-full bg-github-dark rounded-full h-2.5"><div class="h-2.5 rounded-full bg-blue-500" style="width:0px"></div></div></div><div id="skill-typescript-4" class="bg-github-light rounded-lg p-4 border border-github-border skill-item transition-all duration-300" style="opacity:0;transform:translateY(20px)"><div class="flex items-center gap-3 mb-3"><div class="skill-icon flex items-center justify-center p-2 rounded-md bg-github-darker"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#c9d1d9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-code skill-icon-svg"><polyline points="16 18 22 12 16 6"></polyline><polyline points="8 6 2 12 8 18"></polyline></svg></div><div class="flex justify-between items-center w-full"><span class="text-white font-medium">TYPESCRIPT</span><span class="text-sm text-neon-green">92<!-- -->%</span></div></div><div class="w-full bg-github-dark rounded-full h-2.5"><div class="h-2.5 rounded-full bg-blue-700" style="width:0px"></div></div></div><div id="skill-rust-5" class="bg-github-light rounded-lg p-4 border border-github-border skill-item transition-all duration-300" style="opacity:0;transform:translateY(20px)"><div class="flex items-center gap-3 mb-3"><div class="skill-icon flex items-center justify-center p-2 rounded-md bg-github-darker"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#c9d1d9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-code skill-icon-svg"><path d="M10 12.5 8 15l2 2.5"></path><path d="m14 12.5 2 2.5-2 2.5"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7z"></path></svg></div><div class="flex justify-between items-center w-full"><span class="text-white font-medium">RUST</span><span class="text-sm text-neon-green">60<!-- -->%</span></div></div><div class="w-full bg-github-dark rounded-full h-2.5"><div class="h-2.5 rounded-full bg-orange-700" style="width:0px"></div></div></div><div id="skill-powershell-6" class="bg-github-light rounded-lg p-4 border border-github-border skill-item transition-all duration-300" style="opacity:0;transform:translateY(20px)"><div class="flex items-center gap-3 mb-3"><div class="skill-icon flex items-center justify-center p-2 rounded-md bg-github-darker"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#c9d1d9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-terminal skill-icon-svg"><polyline points="4 17 10 11 4 5"></polyline><line x1="12" x2="20" y1="19" y2="19"></line></svg></div><div class="flex justify-between items-center w-full"><span class="text-white font-medium">POWERSHELL</span><span class="text-sm text-neon-green">75<!-- -->%</span></div></div><div class="w-full bg-github-dark rounded-full h-2.5"><div class="h-2.5 rounded-full bg-blue-300" style="width:0px"></div></div></div><div id="skill-bash-script-7" class="bg-github-light rounded-lg p-4 border border-github-border skill-item transition-all duration-300" style="opacity:0;transform:translateY(20px)"><div class="flex items-center gap-3 mb-3"><div class="skill-icon flex items-center justify-center p-2 rounded-md bg-github-darker"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#c9d1d9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-terminal skill-icon-svg"><polyline points="4 17 10 11 4 5"></polyline><line x1="12" x2="20" y1="19" y2="19"></line></svg></div><div class="flex justify-between items-center w-full"><span class="text-white font-medium">BASH SCRIPT</span><span class="text-sm text-neon-green">80<!-- -->%</span></div></div><div class="w-full bg-github-dark rounded-full h-2.5"><div class="h-2.5 rounded-full bg-gray-700" style="width:0px"></div></div></div></div></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R4ssflb»-trigger-Frontend Development" hidden="" id="radix-«R4ssflb»-content-Frontend Development" tabindex="0" class="mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R4ssflb»-trigger-Backend Development" hidden="" id="radix-«R4ssflb»-content-Backend Development" tabindex="0" class="mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R4ssflb»-trigger-Cloud &amp; Deployment" hidden="" id="radix-«R4ssflb»-content-Cloud &amp; Deployment" tabindex="0" class="mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R4ssflb»-trigger-Databases" hidden="" id="radix-«R4ssflb»-content-Databases" tabindex="0" class="mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R4ssflb»-trigger-DevOps &amp; Tools" hidden="" id="radix-«R4ssflb»-content-DevOps &amp; Tools" tabindex="0" class="mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R4ssflb»-trigger-Data Science &amp; ML" hidden="" id="radix-«R4ssflb»-content-Data Science &amp; ML" tabindex="0" class="mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"></div><div data-state="inactive" data-orientation="horizontal" role="tabpanel" aria-labelledby="radix-«R4ssflb»-trigger-UI/UX &amp; Design" hidden="" id="radix-«R4ssflb»-content-UI/UX &amp; Design" tabindex="0" class="mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"></div></div></div></div><div class="mt-12"><h3 class="text-xl text-white font-bold mb-4">Top Skills at a Glance</h3><div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4"><div id="top-skill-javascript-0" class="bg-github-light/30 p-3 rounded-lg text-center flex flex-col items-center transition-all duration-300 hover:bg-github-light/50" style="opacity:0;transform:scale(0.9)"><div class="skill-icon flex items-center justify-center p-2 rounded-md bg-github-darker"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#3fb950" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-code skill-icon-svg"><polyline points="16 18 22 12 16 6"></polyline><polyline points="8 6 2 12 8 18"></polyline></svg></div><span class="text-white mt-2">JAVASCRIPT</span><span class="text-neon-green text-sm">95<!-- -->%</span></div><div id="top-skill-react-1" class="bg-github-light/30 p-3 rounded-lg text-center flex flex-col items-center transition-all duration-300 hover:bg-github-light/50" style="opacity:0;transform:scale(0.9)"><div class="skill-icon flex items-center justify-center p-2 rounded-md bg-github-darker"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#3fb950" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-code skill-icon-svg"><polyline points="16 18 22 12 16 6"></polyline><polyline points="8 6 2 12 8 18"></polyline></svg></div><span class="text-white mt-2">REACT</span><span class="text-neon-green text-sm">92<!-- -->%</span></div><div id="top-skill-node-js-2" class="bg-github-light/30 p-3 rounded-lg text-center flex flex-col items-center transition-all duration-300 hover:bg-github-light/50" style="opacity:0;transform:scale(0.9)"><div class="skill-icon flex items-center justify-center p-2 rounded-md bg-github-darker"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#3fb950" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-server skill-icon-svg"><rect width="20" height="8" x="2" y="2" rx="2" ry="2"></rect><rect width="20" height="8" x="2" y="14" rx="2" ry="2"></rect><line x1="6" x2="6.01" y1="6" y2="6"></line><line x1="6" x2="6.01" y1="18" y2="18"></line></svg></div><span class="text-white mt-2">NODE.JS</span><span class="text-neon-green text-sm">90<!-- -->%</span></div><div id="top-skill-python-3" class="bg-github-light/30 p-3 rounded-lg text-center flex flex-col items-center transition-all duration-300 hover:bg-github-light/50" style="opacity:0;transform:scale(0.9)"><div class="skill-icon flex items-center justify-center p-2 rounded-md bg-github-darker"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#3fb950" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-code skill-icon-svg"><path d="M10 12.5 8 15l2 2.5"></path><path d="m14 12.5 2 2.5-2 2.5"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7z"></path></svg></div><span class="text-white mt-2">PYTHON</span><span class="text-neon-green text-sm">90<!-- -->%</span></div><div id="top-skill-typescript-4" class="bg-github-light/30 p-3 rounded-lg text-center flex flex-col items-center transition-all duration-300 hover:bg-github-light/50" style="opacity:0;transform:scale(0.9)"><div class="skill-icon flex items-center justify-center p-2 rounded-md bg-github-darker"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#3fb950" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-code skill-icon-svg"><polyline points="16 18 22 12 16 6"></polyline><polyline points="8 6 2 12 8 18"></polyline></svg></div><span class="text-white mt-2">TYPESCRIPT</span><span class="text-neon-green text-sm">92<!-- -->%</span></div><div id="top-skill-mongodb-5" class="bg-github-light/30 p-3 rounded-lg text-center flex flex-col items-center transition-all duration-300 hover:bg-github-light/50" style="opacity:0;transform:scale(0.9)"><div class="skill-icon flex items-center justify-center p-2 rounded-md bg-github-darker"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#3fb950" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-database skill-icon-svg"><ellipse cx="12" cy="5" rx="9" ry="3"></ellipse><path d="M3 5V19A9 3 0 0 0 21 19V5"></path><path d="M3 12A9 3 0 0 0 21 12"></path></svg></div><span class="text-white mt-2">MONGODB</span><span class="text-neon-green text-sm">92<!-- -->%</span></div></div></div></div></section></section><section id="projects"><section id="projects" class="py-20 bg-github-light"><div class="section-container"><h2 class="section-title" style="opacity:0;transform:translateY(20px)">Projects</h2><div class="flex flex-wrap gap-3 mb-8 justify-center sm:justify-start" style="opacity:0;transform:translateY(10px)"><button class="px-4 py-2 rounded-md text-sm transition-colors bg-neon-green text-black font-medium">All Projects</button><button class="px-4 py-2 rounded-md text-sm transition-colors bg-github-dark text-github-text hover:bg-github-dark/80">Web</button><button class="px-4 py-2 rounded-md text-sm transition-colors bg-github-dark text-github-text hover:bg-github-dark/80">Apps</button><button class="px-4 py-2 rounded-md text-sm transition-colors bg-github-dark text-github-text hover:bg-github-dark/80">Design</button></div><div class="grid grid-cols-1 md:grid-cols-2 gap-6"><div style="opacity:0;transform:translateY(20px)"><div class="relative h-full" style="perspective:1000px;transform-style:preserve-3d" tabindex="0"><div style="transform-style:preserve-3d;box-shadow:0 0px 0px rgba(63, 185, 80, 0.4);transform:none" class="h-full w-full transition-colors duration-300"><div class="bg-github-dark border border-github-border rounded-lg overflow-hidden h-full transition-all duration-300"><div class="aspect-video w-full overflow-hidden"><img src="https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?crop=entropy&amp;cs=tinysrgb&amp;fit=crop&amp;fm=jpg&amp;h=800&amp;ixid=MnwxfDB8MXxyYW5kb218MHx8dGVjaHx8fHx8fDE2MjM2MzYyODE&amp;ixlib=rb-1.2.1&amp;q=80&amp;utm_campaign=api-credit&amp;utm_medium=referral&amp;utm_source=unsplash_source&amp;w=1200" alt="Portfolio" class="w-full h-full object-cover object-center"/></div><div class="p-6"><div class="flex justify-between items-start"><h3 class="text-xl font-bold text-white">Portfolio</h3><div class="flex flex-wrap gap-2"><span class="tech-badge px-2 py-1 text-xs rounded bg-tech-html/20 text-tech-html border border-tech-html/30">HTML</span><span class="tech-badge px-2 py-1 text-xs rounded bg-tech-css/20 text-tech-css border border-tech-css/30">CSS</span><span class="tech-badge px-2 py-1 text-xs rounded bg-yellow-500/20 text-yellow-500 border border-yellow-500/30">JavaScript</span></div></div><p class="mt-4 text-github-text">Personal portfolio website built with HTML and showcasing my projects and skills.</p><div class="mt-6 flex gap-3"><a href="#" class="px-4 py-2 bg-neon-green/20 text-neon-green rounded-md hover:bg-neon-green/30 transition-colors">Demo</a><a href="#" class="px-4 py-2 bg-github-light text-github-text rounded-md hover:bg-github-light/80 transition-colors">Source</a></div></div></div><div class="absolute inset-0 rounded-lg pointer-events-none border border-neon-green" style="opacity:0"></div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="relative h-full" style="perspective:1000px;transform-style:preserve-3d" tabindex="0"><div style="transform-style:preserve-3d;box-shadow:0 0px 0px rgba(63, 185, 80, 0.4);transform:none" class="h-full w-full transition-colors duration-300"><div class="bg-github-dark border border-github-border rounded-lg overflow-hidden h-full transition-all duration-300"><div class="aspect-video w-full overflow-hidden"><img src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?crop=entropy&amp;cs=tinysrgb&amp;fit=crop&amp;fm=jpg&amp;h=800&amp;ixid=MnwxfDB8MXxyYW5kb218MHx8dGVjaHx8fHx8fDE2MjM2MzYyODE&amp;ixlib=rb-1.2.1&amp;q=80&amp;utm_campaign=api-credit&amp;utm_medium=referral&amp;utm_source=unsplash_source&amp;w=1200" alt="SNW" class="w-full h-full object-cover object-center"/></div><div class="p-6"><div class="flex justify-between items-start"><h3 class="text-xl font-bold text-white">SNW</h3><div class="flex flex-wrap gap-2"><span class="tech-badge px-2 py-1 text-xs rounded bg-tech-css/20 text-tech-css border border-tech-css/30">CSS</span><span class="tech-badge px-2 py-1 text-xs rounded bg-blue-400/20 text-blue-400 border border-blue-400/30">React</span><span class="tech-badge px-2 py-1 text-xs rounded bg-teal-500/20 text-teal-500 border border-teal-500/30">Tailwind</span></div></div><p class="mt-4 text-github-text">A CSS-based interactive web application with modern design principles.</p><div class="mt-6 flex gap-3"><a href="#" class="px-4 py-2 bg-neon-green/20 text-neon-green rounded-md hover:bg-neon-green/30 transition-colors">Demo</a><a href="#" class="px-4 py-2 bg-github-light text-github-text rounded-md hover:bg-github-light/80 transition-colors">Source</a></div></div></div><div class="absolute inset-0 rounded-lg pointer-events-none border border-neon-green" style="opacity:0"></div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="relative h-full" style="perspective:1000px;transform-style:preserve-3d" tabindex="0"><div style="transform-style:preserve-3d;box-shadow:0 0px 0px rgba(63, 185, 80, 0.4);transform:none" class="h-full w-full transition-colors duration-300"><div class="bg-github-dark border border-github-border rounded-lg overflow-hidden h-full transition-all duration-300"><div class="aspect-video w-full overflow-hidden"><img src="https://images.unsplash.com/photo-1461749280684-dccba630e2f6?crop=entropy&amp;cs=tinysrgb&amp;fit=crop&amp;fm=jpg&amp;h=800&amp;ixid=MnwxfDB8MXxyYW5kb218MHx8cHJvZ3JhbW1pbmd8fHx8fHwxNjIzNjM2MzU4&amp;ixlib=rb-1.2.1&amp;q=80&amp;utm_campaign=api-credit&amp;utm_medium=referral&amp;utm_source=unsplash_source&amp;w=1200" alt="Nirmaan" class="w-full h-full object-cover object-center"/></div><div class="p-6"><div class="flex justify-between items-start"><h3 class="text-xl font-bold text-white">Nirmaan</h3><div class="flex flex-wrap gap-2"><span class="tech-badge px-2 py-1 text-xs rounded bg-tech-css/20 text-tech-css border border-tech-css/30">CSS</span><span class="tech-badge px-2 py-1 text-xs rounded bg-yellow-500/20 text-yellow-500 border border-yellow-500/30">JavaScript</span><span class="tech-badge px-2 py-1 text-xs rounded bg-purple-500/20 text-purple-500 border border-purple-500/30">Design</span></div></div><p class="mt-4 text-github-text">A CSS framework for creating responsive and accessible web interfaces.</p><div class="mt-6 flex gap-3"><a href="#" class="px-4 py-2 bg-neon-green/20 text-neon-green rounded-md hover:bg-neon-green/30 transition-colors">Demo</a><a href="#" class="px-4 py-2 bg-github-light text-github-text rounded-md hover:bg-github-light/80 transition-colors">Source</a></div></div></div><div class="absolute inset-0 rounded-lg pointer-events-none border border-neon-green" style="opacity:0"></div></div></div></div><div style="opacity:0;transform:translateY(20px)"><div class="relative h-full" style="perspective:1000px;transform-style:preserve-3d" tabindex="0"><div style="transform-style:preserve-3d;box-shadow:0 0px 0px rgba(63, 185, 80, 0.4);transform:none" class="h-full w-full transition-colors duration-300"><div class="bg-github-dark border border-github-border rounded-lg overflow-hidden h-full transition-all duration-300"><div class="aspect-video w-full overflow-hidden"><img src="https://images.unsplash.com/photo-1498050108023-c5249f4df085?crop=entropy&amp;cs=tinysrgb&amp;fit=crop&amp;fm=jpg&amp;h=800&amp;ixid=MnwxfDB8MXxyYW5kb218MHx8Y29kZXx8fHx8fDE2MjM2MzYzNzg&amp;ixlib=rb-1.2.1&amp;q=80&amp;utm_campaign=api-credit&amp;utm_medium=referral&amp;utm_source=unsplash_source&amp;w=1200" alt="Storage-NextJs" class="w-full h-full object-cover object-center"/></div><div class="p-6"><div class="flex justify-between items-start"><h3 class="text-xl font-bold text-white">Storage-NextJs</h3><div class="flex flex-wrap gap-2"><span class="tech-badge px-2 py-1 text-xs rounded bg-tech-ts/20 text-tech-ts border border-tech-ts/30">TypeScript</span><span class="tech-badge px-2 py-1 text-xs rounded bg-black/40 text-white border border-white/30">Next.js</span><span class="tech-badge px-2 py-1 text-xs rounded bg-blue-600/20 text-blue-600 border border-blue-600/30">Cloud</span></div></div><p class="mt-4 text-github-text">A NextJS-based storage solution with TypeScript integration.</p><div class="mt-6 flex gap-3"><a href="#" class="px-4 py-2 bg-neon-green/20 text-neon-green rounded-md hover:bg-neon-green/30 transition-colors">Demo</a><a href="#" class="px-4 py-2 bg-github-light text-github-text rounded-md hover:bg-github-light/80 transition-colors">Source</a></div></div></div><div class="absolute inset-0 rounded-lg pointer-events-none border border-neon-green" style="opacity:0"></div></div></div></div></div><div class="mt-10 text-center" style="opacity:0;transform:translateY(20px)"><a href="#contact" class="inline-flex items-center px-6 py-3 bg-neon-green text-black font-medium rounded-md hover:bg-neon-green/90 transition-colors"><span>Interested in working together?</span><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg></a></div></div></section></section><section id="experience"><section id="experience" class="py-20 bg-github-dark"><div class="section-container"><h2 class="section-title" style="opacity:0;transform:translateY(20px)">Experience</h2><div class="flex justify-center mb-12"><div class="inline-flex rounded-md shadow-sm p-1 bg-github-light"><button class="px-4 py-2 text-sm font-medium rounded-md bg-neon-green text-black">Work Experience</button><button class="px-4 py-2 text-sm font-medium rounded-md text-github-text hover:text-white">Education</button><button class="px-4 py-2 text-sm font-medium rounded-md text-github-text hover:text-white">Certifications</button><button class="px-4 py-2 text-sm font-medium rounded-md text-github-text hover:text-white">Achievements</button></div></div><div><div class="space-y-4" style="opacity:0"><h3 class="text-xl font-bold text-white flex items-center" style="opacity:0;transform:translateY(20px)"><span class="inline-block w-3 h-3 bg-neon-green rounded-full mr-2"></span>Work Experience</h3><div class="bg-github-light rounded-lg border border-github-border overflow-hidden transition-all duration-300" style="opacity:0;transform:translateY(20px)"><div data-state="closed"><div class="p-6"><div class="flex flex-col sm:flex-row sm:justify-between sm:items-center"><div><h4 class="font-semibold text-white text-lg">Field Associate</h4><p class="text-neon-green">Sachetparyant</p></div><div class="mt-2 sm:mt-0 text-right"><p class="text-github-text text-sm">Part-time</p><p class="mt-1 text-github-text text-sm">Sep 2021 - Jul 2023</p></div></div><button type="button" aria-controls="radix-«R138tcflb»" aria-expanded="false" data-state="closed" class="mt-4 text-sm text-github-text hover:text-white transition-colors flex items-center">Show more<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2 transition-transform "><path d="m6 9 6 6 6-6"></path></svg></button></div><div data-state="closed" id="radix-«R138tcflb»" hidden=""></div></div></div><div class="bg-github-light rounded-lg border border-github-border overflow-hidden transition-all duration-300" style="opacity:0;transform:translateY(20px)"><div data-state="closed"><div class="p-6"><div class="flex flex-col sm:flex-row sm:justify-between sm:items-center"><div><h4 class="font-semibold text-white text-lg">Field Associate</h4><p class="text-neon-green">Sachetparyant</p></div><div class="mt-2 sm:mt-0 text-right"><p class="text-github-text text-sm">Part-time</p><p class="mt-1 text-github-text text-sm">Sep 2021 - Apr 2022</p></div></div><button type="button" aria-controls="radix-«R158tcflb»" aria-expanded="false" data-state="closed" class="mt-4 text-sm text-github-text hover:text-white transition-colors flex items-center">Show more<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2 transition-transform "><path d="m6 9 6 6 6-6"></path></svg></button></div><div data-state="closed" id="radix-«R158tcflb»" hidden=""></div></div></div><div class="bg-github-light rounded-lg border border-github-border overflow-hidden transition-all duration-300" style="opacity:0;transform:translateY(20px)"><div data-state="closed"><div class="p-6"><div class="flex flex-col sm:flex-row sm:justify-between sm:items-center"><div><h4 class="font-semibold text-white text-lg">Field Associate</h4><p class="text-neon-green">Sachetparyant</p></div><div class="mt-2 sm:mt-0 text-right"><p class="text-github-text text-sm">Internship</p><p class="mt-1 text-github-text text-sm">Sep 2021 - Sep 2021</p></div></div><button type="button" aria-controls="radix-«R178tcflb»" aria-expanded="false" data-state="closed" class="mt-4 text-sm text-github-text hover:text-white transition-colors flex items-center">Show more<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2 transition-transform "><path d="m6 9 6 6 6-6"></path></svg></button></div><div data-state="closed" id="radix-«R178tcflb»" hidden=""></div></div></div><div class="bg-github-light rounded-lg border border-github-border overflow-hidden transition-all duration-300" style="opacity:0;transform:translateY(20px)"><div data-state="closed"><div class="p-6"><div class="flex flex-col sm:flex-row sm:justify-between sm:items-center"><div><h4 class="font-semibold text-white text-lg">Executive</h4><p class="text-neon-green">Sachetparyant</p></div><div class="mt-2 sm:mt-0 text-right"><p class="text-github-text text-sm">Internship</p><p class="mt-1 text-github-text text-sm">Jul 2021 - Sep 2021</p></div></div><button type="button" aria-controls="radix-«R198tcflb»" aria-expanded="false" data-state="closed" class="mt-4 text-sm text-github-text hover:text-white transition-colors flex items-center">Show more<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2 transition-transform "><path d="m6 9 6 6 6-6"></path></svg></button></div><div data-state="closed" id="radix-«R198tcflb»" hidden=""></div></div></div></div></div></div></section></section><section id="resume"><section id="resume" class="py-20 bg-github-dark relative"><div class="section-container"><h2 class="section-title mb-12">Resume</h2><div class="grid grid-cols-1 md:grid-cols-2 gap-8"><div class="bg-github-light rounded-lg p-6 border border-github-border card-hover" style="opacity:0;transform:translateY(20px)"><div class="flex items-start justify-between mb-4"><div class="flex items-center"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-file-text text-neon-green mr-3"><path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z"></path><path d="M14 2v4a2 2 0 0 0 2 2h4"></path><path d="M10 9H8"></path><path d="M16 13H8"></path><path d="M16 17H8"></path></svg><h3 class="text-xl font-semibold text-white">My Resume</h3></div><span class="px-3 py-1 bg-neon-green/20 text-neon-green text-xs rounded-full">PDF</span></div><p class="text-github-text mb-6">Check out my professional experience, skills, and educational background. Download the PDF or view it directly on this page.</p><div class="flex flex-wrap gap-3"><button class="flex items-center gap-2 px-4 py-2 bg-neon-green text-black font-medium rounded-md hover:bg-neon-green/90 transition-all" type="button" aria-haspopup="dialog" aria-expanded="false" aria-controls="radix-«R1spkflb»" data-state="closed"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-eye"><path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"></path><circle cx="12" cy="12" r="3"></circle></svg>View Resume</button><a href="/resume.pdf" download="GREENHACKER_Resume.pdf" class="flex items-center gap-2 px-4 py-2 bg-transparent border border-neon-green text-neon-green font-medium rounded-md hover:bg-neon-green/10 transition-all"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-download"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points="7 10 12 15 17 10"></polyline><line x1="12" x2="12" y1="15" y2="3"></line></svg>Download PDF</a></div></div><div class="bg-github-light rounded-lg p-6 border border-github-border" style="opacity:0;transform:translateY(20px)"><div class="flex items-center justify-between mb-6"><h3 class="text-xl font-semibold text-white">Highlights</h3><button class="px-3 py-1 bg-neon-purple/20 text-neon-purple text-xs rounded-full hover:bg-neon-purple/30 transition-colors flex items-center gap-1"><span>+ Generate with Gemini</span></button></div><ul class="space-y-4"><li class="flex items-start p-3 rounded-md transition-all cursor-pointer hover:bg-github-border/10" style="opacity:0;transform:translateX(-20px)"><div class="bg-github-dark/50 p-2 rounded-md mr-3"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-award text-neon-green"><path d="m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526"></path><circle cx="12" cy="8" r="6"></circle></svg></div><div><div class="flex items-center"><span class="text-neon-green text-xs uppercase tracking-wider">Development</span></div><span class="text-github-text block mt-1">Full Stack Development with React, Node.js, and TypeScript</span></div></li><li class="flex items-start p-3 rounded-md transition-all cursor-pointer hover:bg-github-border/10" style="opacity:0;transform:translateX(-20px)"><div class="bg-github-dark/50 p-2 rounded-md mr-3"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-book-open text-neon-green"><path d="M12 7v14"></path><path d="M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z"></path></svg></div><div><div class="flex items-center"><span class="text-neon-green text-xs uppercase tracking-wider">AI/ML</span></div><span class="text-github-text block mt-1">Machine Learning specialization with PyTorch and TensorFlow</span></div></li><li class="flex items-start p-3 rounded-md transition-all cursor-pointer hover:bg-github-border/10" style="opacity:0;transform:translateX(-20px)"><div class="bg-github-dark/50 p-2 rounded-md mr-3"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-coffee text-neon-green"><path d="M10 2v2"></path><path d="M14 2v2"></path><path d="M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1"></path><path d="M6 2v2"></path></svg></div><div><div class="flex items-center"><span class="text-neon-green text-xs uppercase tracking-wider">Experience</span></div><span class="text-github-text block mt-1">5+ years experience working with distributed teams</span></div></li><li class="flex items-start p-3 rounded-md transition-all cursor-pointer hover:bg-github-border/10" style="opacity:0;transform:translateX(-20px)"><div class="bg-github-dark/50 p-2 rounded-md mr-3"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-award text-neon-green"><path d="m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526"></path><circle cx="12" cy="8" r="6"></circle></svg></div><div><div class="flex items-center"><span class="text-neon-green text-xs uppercase tracking-wider">Community</span></div><span class="text-github-text block mt-1">Open Source contributor to various GitHub projects</span></div></li><li class="flex items-start p-3 rounded-md transition-all cursor-pointer hover:bg-github-border/10" style="opacity:0;transform:translateX(-20px)"><div class="bg-github-dark/50 p-2 rounded-md mr-3"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-book-open text-neon-green"><path d="M12 7v14"></path><path d="M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z"></path></svg></div><div><div class="flex items-center"><span class="text-neon-green text-xs uppercase tracking-wider">Speaking</span></div><span class="text-github-text block mt-1">Conference speaker on AI and web technologies</span></div></li></ul></div></div></div></section></section><section id="stats"><section id="stats" class="py-20 bg-github-light"><div class="section-container"><h2 class="section-title" style="opacity:0;transform:translateY(20px)">GitHub Stats</h2><div class="mb-10 flex justify-center"><div class="flex rounded-lg overflow-hidden border border-github-border"><button class="px-4 py-2 bg-neon-green text-black">Contributions</button><button class="px-4 py-2 bg-github-dark text-github-text">Languages</button><button class="px-4 py-2 bg-github-dark text-github-text">Top Repos</button></div></div><div class="grid grid-cols-1 lg:grid-cols-2 gap-8" style="opacity:0"><div class="bg-github-dark p-6 rounded-xl border border-github-border" style="opacity:0;transform:translateY(20px)"><h3 class="text-xl font-bold text-white mb-6">Green Hacker&#x27;s GitHub Stats</h3><div class="grid grid-cols-2 gap-4"><div class="p-4 bg-github-light/50 rounded-lg hover:bg-github-light transition-colors"><p class="text-sm text-github-text">Total Stars Earned:</p><p class="text-2xl font-bold text-white" style="opacity:0">47</p></div><div class="p-4 bg-github-light/50 rounded-lg hover:bg-github-light transition-colors"><p class="text-sm text-github-text">Total Commits (2025):</p><p class="text-2xl font-bold text-white" style="opacity:0">430</p></div><div class="p-4 bg-github-light/50 rounded-lg hover:bg-github-light transition-colors"><p class="text-sm text-github-text">Total PRs:</p><p class="text-2xl font-bold text-white" style="opacity:0">28</p></div><div class="p-4 bg-github-light/50 rounded-lg hover:bg-github-light transition-colors"><p class="text-sm text-github-text">Total Issues:</p><p class="text-2xl font-bold text-white" style="opacity:0">15</p></div><div class="p-4 bg-github-light/50 rounded-lg col-span-2 hover:bg-github-light transition-colors"><p class="text-sm text-github-text">Contributed to (last year):</p><p class="text-2xl font-bold text-white" style="opacity:0">12<!-- --> Open Source Projects</p></div></div><div class="mt-8 grid grid-cols-3 gap-4"><div class="p-5 flex flex-col items-center justify-center"><p class="text-3xl font-bold text-white" style="opacity:0;transform:translateY(20px)">623</p><p class="text-sm text-github-text text-center mt-2">This Year</p><p class="text-xs text-github-text text-center mt-1">Contributions</p></div><div class="p-5 flex flex-col items-center justify-center"><div class="w-20 h-20 rounded-full bg-github-light flex items-center justify-center border-4 border-neon-green/70" style="transform:scale(0)"><p class="text-3xl font-bold text-white">3</p></div><p class="text-sm text-github-text text-center mt-2">Current Streak</p></div><div class="p-5 flex flex-col items-center justify-center"><p class="text-3xl font-bold text-white" style="opacity:0;transform:translateY(20px)">17</p><p class="text-sm text-github-text text-center mt-2">Longest Streak</p><p class="text-xs text-github-text text-center mt-1">Apr 8 - Apr 24</p></div></div></div><div class="bg-github-dark p-6 rounded-xl border border-github-border" style="opacity:0;transform:translateY(20px)"><h3 class="text-xl font-bold text-white mb-6">Contribution Activity</h3><div class="h-64"><div class="recharts-responsive-container" style="width:100%;height:100%;min-width:0"></div></div><div class="mt-8"><h4 class="text-lg font-semibold text-white mb-4">Recent Activity</h4><div class="space-y-4"><div class="flex items-start space-x-3" style="opacity:0;transform:translateX(-20px)"><div class="w-6 h-6 bg-github-light rounded-full flex items-center justify-center flex-shrink-0 mt-1"><svg class="w-4 h-4 text-neon-green" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg></div><div><p class="text-white">Created 14 commits in 5 repositories</p><p class="text-sm text-github-text">Last week</p></div></div><div class="flex items-start space-x-3" style="opacity:0;transform:translateX(-20px)"><div class="w-6 h-6 bg-github-light rounded-full flex items-center justify-center flex-shrink-0 mt-1"><svg class="w-4 h-4 text-neon-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg></div><div><p class="text-white">Created 2 new repositories</p><p class="text-sm text-github-text">This month</p></div></div></div><button class="mt-6 w-full py-2 border border-github-border rounded-md text-github-text hover:bg-github-light transition-colors" tabindex="0">Show more activity</button></div></div></div></div></section></section><section id="contact"><section id="contact" class="py-20 bg-github-dark"><div class="section-container"><h2 class="section-title" style="opacity:0;transform:translateY(20px)">Contact</h2><div class="text-center max-w-xl mx-auto mb-12" style="opacity:0;transform:translateY(20px)"><p class="text-lg text-github-text">Feel free to reach out if you&#x27;d like to collaborate, discuss tech, or share some awesome ideas!</p></div><div class="grid grid-cols-1 md:grid-cols-2 gap-8"><div class="space-y-6" style="opacity:0;transform:translateX(-30px)"><h3 class="text-2xl font-bold text-white">Get in Touch</h3><p class="text-github-text">Whether you have a project in mind, a question about my work, or just want to say hi, I&#x27;d love to hear from you.</p><div class="space-y-4"><div class="flex items-start space-x-4"><div class="w-10 h-10 rounded-full bg-github-light flex items-center justify-center flex-shrink-0"><svg class="w-5 h-5 text-neon-green" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path></svg></div><div><h4 class="font-medium text-white">Email</h4><p class="text-github-text"><a href="mailto:<EMAIL>"><EMAIL></a></p></div></div><div class="flex items-start space-x-4"><div class="w-10 h-10 rounded-full bg-github-light flex items-center justify-center flex-shrink-0"><svg class="w-5 h-5 text-neon-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path></svg></div><div><h4 class="font-medium text-white">Location</h4><p class="text-github-text">Pune, Maharashtra</p></div></div><div class="flex items-start space-x-4"><div class="w-10 h-10 rounded-full bg-github-light flex items-center justify-center flex-shrink-0"><svg class="w-5 h-5 text-neon-purple" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path></svg></div><div><h4 class="font-medium text-white">Portfolio</h4><p class="text-github-text">https://portfolio.greenhacker.tech/</p></div></div></div><div class="pt-8"><h3 class="text-xl font-bold text-white mb-4">Connect With Me</h3><div class="flex space-x-4"><a href="https://www.linkedin.com/in/harsh-hirawat-b657061b7/" target="_blank" rel="noreferrer" class="w-12 h-12 rounded-full bg-github-light flex items-center justify-center hover:bg-neon-blue/20 transition-colors"><svg class="w-6 h-6 text-neon-blue" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"></path></svg></a><a href="https://instagram.com/harsh_hirawat" target="_blank" rel="noreferrer" class="w-12 h-12 rounded-full bg-github-light flex items-center justify-center hover:bg-neon-pink/20 transition-colors"><svg class="w-6 h-6 text-neon-pink" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"></path></svg></a><a href="https://github.com/GreenHacker420" target="_blank" rel="noreferrer" class="w-12 h-12 rounded-full bg-github-light flex items-center justify-center hover:bg-white/10 transition-colors"><svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"></path></svg></a></div></div></div><div style="opacity:0;transform:translateX(30px)"><form class="bg-github-light p-6 rounded-lg border border-github-border"><h3 class="text-2xl font-bold text-white mb-6">Send a Message</h3><div class="space-y-4"><div><label for="name" class="block text-github-text text-sm font-medium mb-2">Your Name</label><input type="text" id="name" class="w-full px-4 py-3 bg-github-dark border border-github-border rounded-lg focus:outline-none focus:ring-2 focus:ring-neon-green/50 text-white" placeholder="John Doe" required="" name="name" value=""/></div><div><label for="email" class="block text-github-text text-sm font-medium mb-2">Your Email</label><input type="email" id="email" class="w-full px-4 py-3 bg-github-dark border border-github-border rounded-lg focus:outline-none focus:ring-2 focus:ring-neon-green/50 text-white" placeholder="<EMAIL>" required="" name="email" value=""/></div><div><label for="subject" class="block text-github-text text-sm font-medium mb-2">Subject</label><input type="text" id="subject" class="w-full px-4 py-3 bg-github-dark border border-github-border rounded-lg focus:outline-none focus:ring-2 focus:ring-neon-green/50 text-white" placeholder="Project Collaboration" required="" name="subject" value=""/></div><div><label for="message" class="block text-github-text text-sm font-medium mb-2">Message</label><textarea id="message" name="message" rows="5" class="w-full px-4 py-3 bg-github-dark border border-github-border rounded-lg focus:outline-none focus:ring-2 focus:ring-neon-green/50 text-white" placeholder="Hi there, I&#x27;d like to talk about..." required=""></textarea></div><button type="submit" class="w-full py-3 rounded-lg font-medium transition-colors bg-neon-green text-black hover:bg-neon-green/90">Send Message</button></div></form></div></div></div></section></section></main><footer class="bg-github-darker border-t border-github-border" style="opacity:0"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12"><div class="grid grid-cols-1 md:grid-cols-3 gap-8"><div class="space-y-4"><div class="flex items-center space-x-2"><div class="w-10 h-10 rounded-full bg-gradient-to-br from-neon-green to-neon-blue flex items-center justify-center"><span class="font-mono font-bold text-white">GH</span></div><span class="font-bold text-xl text-white">GreenHacker</span></div><p class="text-sm text-github-text mt-4 max-w-sm">Passionate developer and open-source contributor currently working on a photo-sharing platform with face recognition.</p></div><div><h3 class="text-white font-medium mb-4">Quick Links</h3><ul class="space-y-2"><li><a href="#about" class="text-github-text hover:text-white transition-colors hover:underline">About</a></li><li><a href="#projects" class="text-github-text hover:text-white transition-colors hover:underline">Projects</a></li><li><a href="#skills" class="text-github-text hover:text-white transition-colors hover:underline">Skills</a></li><li><a href="#experience" class="text-github-text hover:text-white transition-colors hover:underline">Experience</a></li><li><a href="#contact" class="text-github-text hover:text-white transition-colors hover:underline">Contact</a></li></ul></div><div><h3 class="text-white font-medium mb-4">Connect With Me</h3><div class="flex space-x-4"><a href="https://instagram.com" target="_blank" rel="noreferrer" class="bg-github-light p-2 rounded-full hover:bg-neon-green/20 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-instagram w-5 h-5 text-github-text hover:text-white"><rect width="20" height="20" x="2" y="2" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path><line x1="17.5" x2="17.51" y1="6.5" y2="6.5"></line></svg></a><a href="https://linkedin.com" target="_blank" rel="noreferrer" class="bg-github-light p-2 rounded-full hover:bg-neon-blue/20 transition-colors"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-linkedin w-5 h-5 text-github-text hover:text-white"><path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path><rect width="4" height="12" x="2" y="9"></rect><circle cx="4" cy="4" r="2"></circle></svg></a></div><div class="mt-4"><p class="text-sm text-github-text">harsh_hirawat</p><p class="text-sm text-github-text mt-1">Pune, Maharashtra</p></div></div></div><div class="mt-8 pt-8 border-t border-github-border text-center"><p class="text-sm text-github-text">© <!-- -->2025<!-- --> GreenHacker. All rights reserved.</p></div></div></footer></div><!--$--><!--/$--><!--$--><!--/$--><button class="fixed bottom-8 right-8 bg-neon-green text-black h-12 w-12 rounded-full flex items-center justify-center shadow-lg hover:scale-110 transition-transform z-50" tabindex="0"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-terminal"><polyline points="4 17 10 11 4 5"></polyline><line x1="12" x2="20" y1="19" y2="19"></line></svg></button><script src="/_next/static/chunks/webpack-18b4e876baa90827.js" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[49552,[\"710\",\"static/chunks/710-276d87e630819ab8.js\",\"107\",\"static/chunks/107-a403c883544f12ca.js\",\"469\",\"static/chunks/469-d5d7bcc5f3e29139.js\",\"177\",\"static/chunks/app/layout-1ce3a74b862cc1a7.js\"],\"Providers\"]\n3:I[87555,[],\"\"]\n4:I[31295,[],\"\"]\n5:I[42979,[\"710\",\"static/chunks/710-276d87e630819ab8.js\",\"390\",\"static/chunks/390-483a04468f2201f1.js\",\"345\",\"static/chunks/app/not-found-2db60d607a4c36d9.js\"],\"default\"]\n6:I[90894,[],\"ClientPageRoot\"]\n7:I[37687,[\"631\",\"static/chunks/c6a54c64-faf36295803f59fc.js\",\"568\",\"static/chunks/b1644e8c-9e7d08c5b2aa5e05.js\",\"592\",\"static/chunks/c15bf2b0-7f3f4bd25724833d.js\",\"710\",\"static/chunks/710-276d87e630819ab8.js\",\"107\",\"static/chunks/107-a403c883544f12ca.js\",\"390\",\"static/chunks/390-483a04468f2201f1.js\",\"394\",\"static/chunks/394-db3255639cb235d7.js\",\"974\",\"static/chunks/app/page-47dc305994e39189.js\"],\"default\"]\na:I[59665,[],\"MetadataBoundary\"]\nc:I[59665,[],\"OutletBoundary\"]\nf:I[74911,[],\"AsyncMetadataOutlet\"]\n11:I[59665,[],\"ViewportBoundary\"]\n13:I[26614,[],\"\"]\n:HL[\"/_next/static/media/e4af272ccee01ff0-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n:HL[\"/_next/static/css/d6541876f5d75465.css\",\"style\"]\n0:{\"P\":null,\"b\":\"_Vm4GCrrloX4M9aSmA41U\",\"p\":\"\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/d6541876f5d75465.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"suppressHydrationWarning\":true,\"children\":[[\"$\",\"head\",null,{\"children\":[[\"$\",\"link\",null,{\"rel\":\"manifest\",\"href\":\"/site.webmanifest\"}],[\"$\",\"meta\",null,{\"name\":\"theme-color\",\"content\":\"#0d1117\"}],[\"$\",\"meta\",null,{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]}],[\"$\",\"body\",null,{\"className\":\"__className_e8ce0c\",\"suppressHydrationWarning\":true,\"children\":[\"$\",\"$L2\",null,{\"children\":[\"$\",\"$L3\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefi"])</script><script>self.__next_f.push([1,"ned\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L4\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[\"$\",\"$L5\",null,{}],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L6\",null,{\"Component\":\"$7\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@8\",\"$@9\"]}],[\"$\",\"$La\",null,{\"children\":\"$Lb\"}],null,[\"$\",\"$Lc\",null,{\"children\":[\"$Ld\",\"$Le\",[\"$\",\"$Lf\",null,{\"promise\":\"$@10\"}]]}]]}],{},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[\"$\",\"$1\",\"vkb3BLMQMTHo7wtq-atxG\",{\"children\":[[\"$\",\"$L11\",null,{\"children\":\"$L12\"}],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"}]]}],null]}],false]],\"m\":\"$undefined\",\"G\":[\"$13\",\"$undefined\"],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"14:\"$Sreact.suspense\"\n15:I[74911,[],\"AsyncMetadata\"]\n8:{}\n9:{}\nb:[\"$\",\"$14\",null,{\"fallback\":null,\"children\":[\"$\",\"$L15\",null,{\"promise\":\"$@16\"}]}]\n"])</script><script>self.__next_f.push([1,"e:null\n"])</script><script>self.__next_f.push([1,"12:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\nd:null\n"])</script><script>self.__next_f.push([1,"16:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"GREENHACKER | Developer Portfolio\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Full-stack developer portfolio showcasing modern web technologies, AI integration, and innovative projects.\"}],[\"$\",\"meta\",\"2\",{\"name\":\"author\",\"content\":\"GreenHacker\"}],[\"$\",\"meta\",\"3\",{\"name\":\"keywords\",\"content\":\"developer,portfolio,React,Next.js,TypeScript,AI,machine learning\"}],[\"$\",\"meta\",\"4\",{\"name\":\"creator\",\"content\":\"GreenHacker\"}],[\"$\",\"meta\",\"5\",{\"name\":\"publisher\",\"content\":\"GreenHacker\"}],[\"$\",\"meta\",\"6\",{\"name\":\"robots\",\"content\":\"index, follow\"}],[\"$\",\"meta\",\"7\",{\"name\":\"googlebot\",\"content\":\"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1\"}],[\"$\",\"meta\",\"8\",{\"name\":\"google-site-verification\",\"content\":\"your-google-verification-code\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:title\",\"content\":\"GREENHACKER | Developer Portfolio\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:description\",\"content\":\"Full-stack developer portfolio showcasing modern web technologies, AI integration, and innovative projects.\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:url\",\"content\":\"https://greenhacker.dev\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:site_name\",\"content\":\"GreenHacker Portfolio\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:locale\",\"content\":\"en_US\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"15\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:creator\",\"content\":\"@greenhacker\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:title\",\"content\":\"GREENHACKER | Developer Portfolio\"}],[\"$\",\"meta\",\"18\",{\"name\":\"twitter:description\",\"content\":\"Full-stack developer portfolio showcasing modern web technologies, AI integration, and innovative projects.\"}],[\"$\",\"link\",\"19\",{\"rel\":\"shortcut icon\",\"href\":\"/logo.jpg\"}],[\"$\",\"link\",\"20\",{\"rel\":\"icon\",\"href\":\"/logo.jpg\"}],[\"$\",\"link\",\"21\",{\"rel\":\"apple-touch-icon\",\"href\":\"/logo.jpg\"}]],\"error\":null,\"digest\":\"$undefined\"}\n10:{\"metadata\":\"$16:metadata\",\"error\":null,\"digest\":\"$undefined\"}\n"])</script></body></html>